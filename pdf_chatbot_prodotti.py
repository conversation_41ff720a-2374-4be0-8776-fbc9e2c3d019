#!/usr/bin/env python3
"""
PDF Chatbot per Prodotti con Jina Embeddings v4 e ChromaDB

Funzionamento:
1. Chiede all'utente un codice prodotto.
2. Legge tutti i PDF presenti nella cartella 'pdf/{codice_prodotto}/'.
3. Indicizza i PDF in ChromaDB se sono nuovi o modificati.
4. Risponde a domande utilizzando solo i documenti del prodotto selezionato.
"""

import os
import sys
import requests
import chromadb
from chromadb import Documents, EmbeddingFunction, Embeddings
from typing import List, Dict, Optional, Tuple
import fitz  # PyMuPDF
import time
import logging
from pathlib import Path
import google.generativeai as genai
import hashlib
import json
from datetime import datetime
import urllib.parse
from dotenv import load_dotenv
import threading
from concurrent.futures import ThreadPoolExecutor, TimeoutError
import signal
from guardrails_py.guardrail_manager import GuardrailManager
from guardrails_py.input.input_length_control_guardrail import InputLengthControlGuardrail
from guardrails_py.input.language_detection_guardrail import LanguageDetectionGuardrail
from guardrails_py.input.pii_filter_guardrail import PIIFilterGuardrail
from guardrails_py.input.prompt_injection_guardrail import PromptInjectionGuardrail
from guardrails_py.input.inappropriate_content_guardrail import InappropriateContentGuardrail
from guardrails_py.output.harmful_content_filter_guardrail import HarmfulContentFilterGuardrail
from guardrails_py.output.bias_fairness_mitigation_guardrail import BiasFairnessMitigationGuardrail


# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JinaEmbeddingsV4(EmbeddingFunction):
    """Funzione embedding personalizzata per Jina Embeddings v4"""
    
    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v4", max_retries: int = 3):
        self.api_key = api_key
        self.model_name = model_name
        self.api_url = "https://api.jina.ai/v1/embeddings"
        self.max_retries = max_retries
        
    def __call__(self, input: Documents) -> Embeddings:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }
        cleaned_input = [doc.strip()[:8000] for doc in input if isinstance(doc, str) and doc.strip()]
        if not cleaned_input:
            logger.warning("Nessun documento valido per l'embedding")
            return []
        
        data = {'input': cleaned_input, 'model': self.model_name}
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(self.api_url, headers=headers, json=data)
                response.raise_for_status()
                result = response.json()
                return [item['embedding'] for item in result['data']]
            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.warning(f"Tentativo {attempt + 1} fallito, riprovo tra {wait_time}s: {e}")
                    time.sleep(wait_time)
                else:
                    logger.error(f"Errore dopo {self.max_retries} tentativi: {e}")
                    raise e

class FileProcessor:
    """Classe per elaborare i file."""

    @staticmethod
    def extract_text_from_pdf(pdf_path: str) -> List[Dict]:
        pages_data = []
        try:
            with fitz.open(pdf_path) as doc:
                for page_num, page in enumerate(doc):
                    text = page.get_text()
                    if text.strip():
                        pages_data.append({'page_num': page_num + 1, 'text': text})
            logger.info(f"Testo estratto da {pdf_path} ({len(pages_data)} pagine)")
            return pages_data
        except Exception as e:
            logger.error(f"PyMuPDF fallito per {pdf_path}: {e}")
            return []

    @staticmethod
    def extract_text_from_txt(txt_path: str) -> str:
        try:
            with open(txt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Errore nella lettura di {txt_path}: {e}")
            return ""

    @staticmethod
    def extract_text_from_md(md_path: str) -> str:
        try:
            with open(md_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Errore nella lettura di {md_path}: {e}")
            return ""

    @staticmethod
    def extract_text_from_csv(csv_path: str) -> str:
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Errore nella lettura di {csv_path}: {e}")
            return ""

    @staticmethod
    def extract_text_from_image(image_path: str) -> str:
        logger.warning(f"L'elaborazione delle immagini non è ancora implementata per {image_path}. Salto.")
        return ""

    @staticmethod
    def chunk_text_with_pages(pages_data: List[Dict], chunk_size: int = 1000, overlap: int = 200) -> List[Dict]:
        chunks = []
        for page_data in pages_data:
            page_num, text = page_data['page_num'], page_data['text']
            if not text.strip(): continue
            start = 0
            while start < len(text):
                end = start + chunk_size
                chunk_text = text[start:end]
                if chunk_text.strip():
                    chunks.append({'text': chunk_text.strip(), 'page_num': page_num})
                start += chunk_size - overlap
        return chunks

    @staticmethod
    def get_file_hash(file_path: str) -> str:
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Errore nel calcolo dell'hash per {file_path}: {e}")
            return ""

class ProductChatbot:
    """Chatbot che gestisce documenti per specifici codici prodotto."""
    
    def __init__(self, jina_api_key: str, gemini_api_key: str, verbosity_level: int = 3, force_reprocess: bool = False):
        self.jina_api_key = jina_api_key
        self.gemini_api_key = gemini_api_key
        self.verbosity_level = int(verbosity_level)
        self.force_reprocess = force_reprocess
        self.client = chromadb.PersistentClient(path="./chromadb_data")
        self.collection_name = "product_documents_v2"
        self.collection = None
        self.file_processor = FileProcessor()
        self.metadata_file = Path("pdf_metadata.json")
        
        genai.configure(api_key=self.gemini_api_key)
        # Configurazione ottimizzata per performance
        generation_config = genai.types.GenerationConfig(
            temperature=0.7,
            top_p=0.8,
            top_k=40,
            max_output_tokens=2048,  # Limita la lunghezza della risposta
            candidate_count=1
        )

        # Configurazioni di sicurezza ottimizzate
        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        ]

        self.gemini_model = genai.GenerativeModel(
            'gemini-1.5-flash',
            generation_config=generation_config,
            safety_settings=safety_settings
        )

        # Cache per le risposte
        self.response_cache = {}
        self.cache_max_size = 100
        self.cache_lock = threading.Lock()

        # Cache intelligente per query simili
        self.query_cache = {}
        self.similarity_threshold = 0.85  # Soglia di similarità per considerare due query simili

        # Timeout per le chiamate API (in secondi)
        self.api_timeout = 30

        self.guardrail_manager = GuardrailManager(
            input_guardrails=[
                InputLengthControlGuardrail(),
                LanguageDetectionGuardrail(),
                PIIFilterGuardrail(),
                PromptInjectionGuardrail(),
                InappropriateContentGuardrail(),
            ],
            output_guardrails=[
                HarmfulContentFilterGuardrail(),
                BiasFairnessMitigationGuardrail(),
            ]
        )
        
        self._test_api_keys()
        self._setup_collection()

    def _test_api_keys(self):
        try:
            JinaEmbeddingsV4(api_key=self.jina_api_key)(input=['test'])
            logger.info("✅ API key Jina valida.")
        except Exception:
            raise ValueError("❌ API key Jina non valida o scaduta.")
        try:
            self.gemini_model.generate_content("Test")
            logger.info("✅ API key Gemini valida.")
        except Exception:
            raise ValueError("❌ API key Gemini non valida o scaduta.")

    def _setup_collection(self):
        try:
            jina_ef = JinaEmbeddingsV4(api_key=self.jina_api_key)
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                embedding_function=jina_ef
            )
            logger.info(f"✅ Collection ChromaDB '{self.collection_name}' caricata/creata con successo.")
        except Exception as e:
            logger.error(f"Errore fatale nella gestione della collection: {e}")
            raise

    def _load_metadata(self) -> Dict:
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def _save_metadata(self, metadata: Dict):
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        logger.info(f"Metadati salvati in {self.metadata_file}")
    
    def _get_files_for_product(self, product_code: str) -> List[Path]:
        product_dir = Path(f"pdf/{product_code}")
        if not product_dir.is_dir():
            logger.warning(f"La cartella '{product_dir}' non esiste per il codice prodotto '{product_code}'.")
            return []
        
        supported_extensions = [".pdf", ".txt", ".md", ".csv", ".jpg", ".png"]
        files = [p for p in product_dir.rglob("*") if p.suffix.lower() in supported_extensions]
        
        if not files:
            logger.warning(f"Nessun file supportato trovato in '{product_dir}'.")
        return files

    def _check_files_changed(self, files: List[Path]) -> Tuple[bool, List[Path]]:
        stored_metadata = self._load_metadata().get("files", {})
        changed_files = []
        
        for file_path in files:
            path_str = str(file_path)
            current_hash = self.file_processor.get_file_hash(path_str)
            if path_str not in stored_metadata or stored_metadata[path_str] != current_hash:
                changed_files.append(file_path)
        
        return len(changed_files) > 0, changed_files

    def _process_and_load_files(self, files_to_process: List[Path], product_code: str):
        if not self.collection:
            raise ValueError("Collection non inizializzata")

        logger.info(f"Inizio elaborazione per {len(files_to_process)} file del prodotto '{product_code}'...")
        all_metadata = self._load_metadata()
        file_hashes = all_metadata.get("files", {})

        for file_path in files_to_process:
            path_str = str(file_path)
            logger.info(f"🔄 Elaborazione di {path_str}...")
            
            self.collection.delete(where={"source": path_str})

            text_data = ""
            pages_data = []
            if file_path.suffix.lower() == '.pdf':
                pages_data = self.file_processor.extract_text_from_pdf(path_str)
            elif file_path.suffix.lower() == '.txt':
                text_data = self.file_processor.extract_text_from_txt(path_str)
            elif file_path.suffix.lower() == '.md':
                text_data = self.file_processor.extract_text_from_md(path_str)
            elif file_path.suffix.lower() == '.csv':
                text_data = self.file_processor.extract_text_from_csv(path_str)
            elif file_path.suffix.lower() in ['.jpg', '.png']:
                text_data = self.file_processor.extract_text_from_image(path_str)

            if pages_data:
                chunks_data = self.file_processor.chunk_text_with_pages(pages_data)
            elif text_data:
                chunks_data = self.file_processor.chunk_text_with_pages([{'page_num': 1, 'text': text_data}])
            else:
                continue

            if not chunks_data: continue

            doc_chunks = [c['text'] for c in chunks_data]
            metadatas = [{
                "source": path_str,
                "page_num": c['page_num'],
                "product_code": product_code
            } for c in chunks_data]
            ids = [f"{path_str}_chunk_{i}" for i in range(len(chunks_data))]

            batch_size = 50
            for i in range(0, len(doc_chunks), batch_size):
                self.collection.add(
                    ids=ids[i:i + batch_size],
                    documents=doc_chunks[i:i + batch_size],
                    metadatas=metadatas[i:i + batch_size]
                )
                logger.info(f"Aggiunto batch di {len(ids[i:i+batch_size])} chunk per {path_str}")
                time.sleep(1)

            file_hashes[path_str] = self.file_processor.get_file_hash(path_str)
            logger.info(f"✅ {path_str} elaborato e indicizzato per il prodotto '{product_code}'.")

        all_metadata["files"] = file_hashes
        self._save_metadata(all_metadata)

    def prepare_product_documents(self, product_code: str) -> bool:
        """
        Controlla, processa e carica i documenti per un dato codice prodotto.
        Restituisce True se ci sono documenti pronti per la query, altrimenti False.
        """
        files = self._get_files_for_product(product_code)
        if not files:
            return False

        if self.force_reprocess:
            print("🔄 Riprocessamento forzato richiesto...")
            files_to_process = files
        else:
            needs_processing, files_to_process = self._check_files_changed(files)
            if not needs_processing:
                print(f"✅ Documenti per '{product_code}' già aggiornati e presenti nel database.")
                return True
        
        print(f"📝 Rilevate modifiche in {len(files_to_process)} file. Inizio aggiornamento...")
        self._process_and_load_files(files_to_process, product_code)
        print("✅ Aggiornamento completato!")
        return True

    def _create_file_link(self, file_path: str, page_num: int) -> str:
        product_code = Path(file_path).parent.name
        file_name = Path(file_path).name
        encoded_file_name = urllib.parse.quote(file_name)
        return f"/pdf/{product_code}/{encoded_file_name}#page={page_num}"

    def _generate_cache_key(self, query: str, context: str, chat_history: List[Dict]) -> str:
        """Genera una chiave di cache basata su query, contesto e cronologia."""
        # Crea un hash della combinazione di query, contesto e cronologia
        history_str = json.dumps(chat_history, sort_keys=True)
        combined = f"{query}|{context}|{history_str}"
        return hashlib.md5(combined.encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Recupera una risposta dalla cache se disponibile."""
        with self.cache_lock:
            return self.response_cache.get(cache_key)

    def _cache_response(self, cache_key: str, response: str):
        """Salva una risposta nella cache."""
        with self.cache_lock:
            # Rimuovi le voci più vecchie se la cache è piena
            if len(self.response_cache) >= self.cache_max_size:
                # Rimuovi il 20% delle voci più vecchie
                keys_to_remove = list(self.response_cache.keys())[:self.cache_max_size // 5]
                for key in keys_to_remove:
                    del self.response_cache[key]

            self.response_cache[cache_key] = response

    def _generate_answer(self, query: str, search_results: Dict, chat_history: List[Dict]) -> str:
        if not search_results or not search_results['documents'][0]:
            return "❌ Mi dispiace, non riesco a trovare informazioni specifiche per rispondere alla tua domanda su questo prodotto."

        context_parts = []
        for doc, metadata in zip(search_results['documents'][0], search_results['metadatas'][0]):
            source = metadata['source']
            page_num = metadata.get('page_num', 1)
            file_link = self._create_file_link(source, page_num)
            source_ref = f"[📄 {Path(source).name} - Pag. {page_num}]({file_link})"
            context_parts.append(f"{source_ref}\n{doc}")

        context = "\n\n".join(context_parts)

        # Controlla la cache prima di generare una nuova risposta
        cache_key = self._generate_cache_key(query, context, chat_history)
        cached_response = self._get_cached_response(cache_key)
        if cached_response:
            logger.info("🚀 Risposta recuperata dalla cache")
            return cached_response
        
        # Prompt ottimizzato per velocità mantenendo la qualità
        verbosity_map = {1: "conciso", 2: "breve", 3: "bilanciato", 4: "dettagliato", 5: "completo"}
        style = verbosity_map.get(self.verbosity_level, "bilanciato")

        # Limita la cronologia per ridurre la lunghezza del prompt
        recent_history = chat_history[-4:] if len(chat_history) > 4 else chat_history
        history_str = "\n".join([f"{msg['role']}: {msg['parts'][0]}" for msg in recent_history])

        # Prompt ottimizzato e più conciso
        prompt = f"""Sei un esperto tecnico del prodotto. Rispondi in modo {style} e professionale.

{f"CRONOLOGIA: {history_str}" if history_str else ""}

DOMANDA: {query}

INFORMAZIONI:
{context}

REGOLE:
- Rispondi in italiano, tono cordiale
- Mantieni SEMPRE i link [📄 ...] nelle citazioni
- Non usare frasi come "in base ai documenti" o "gentile utente"
- Inizia direttamente con l'informazione
- Se non hai info: "Non ho informazioni specifiche su questo"

RISPOSTA:"""

        try:
            # Chiamata con timeout usando ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=1) as executor:
                def generate_response():
                    chat_session = self.gemini_model.start_chat(history=recent_history)
                    return chat_session.send_message(prompt)

                future = executor.submit(generate_response)
                try:
                    response = future.result(timeout=self.api_timeout)
                    cleaned_response = self._clean_ai_references(response.text)

                    # Salva nella cache
                    self._cache_response(cache_key, cleaned_response)
                    logger.info(f"✅ Risposta generata e salvata in cache (tempo: {self.api_timeout}s)")

                    return cleaned_response

                except TimeoutError:
                    logger.warning(f"⏰ Timeout dopo {self.api_timeout}s - risposta di fallback")
                    return "⏰ La richiesta sta richiedendo più tempo del previsto. Riprova con una domanda più specifica."

        except Exception as e:
            logger.error(f"Errore nella generazione della risposta con Gemini: {e}")
            return f"❌ Errore nella generazione della risposta: {e}"

    def _clean_ai_references(self, response: str) -> str:
        """Rimuove frasi che rivelano la natura AI del chatbot preservando i link ai documenti"""
        import re

        logger.info(f"Original response before cleaning: {response}")

        # Prima proteggiamo i link ai documenti sostituendoli temporaneamente
        link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
        links = re.findall(link_pattern, response)
        protected_response = response

        # Sostituisci i link con placeholder temporanei
        for i, link in enumerate(links):
            placeholder = f"__LINK_PLACEHOLDER_{i}__"
            protected_response = protected_response.replace(link, placeholder, 1)

        # Pattern di frasi da rimuovere o sostituire (più specifici per evitare falsi positivi)
        patterns_to_remove = [
            r"Gentile utente,?\s*",
            r"Egregio utente,?\s*",
            r"Caro utente,?\s*",
            r"(?:^|\s)in base alla documentazione\s*(?:disponibile|fornita|in mio possesso)?,?\s*",
            r"(?:^|\s)in base ai documenti\s*(?:disponibili|forniti|in mio possesso)?,?\s*",
            r"(?:^|\s)secondo i manuali\s*(?:disponibili|forniti|in mio possesso)?,?\s*",
            r"(?:^|\s)dalle informazioni\s*(?:disponibili|fornite|in mio possesso)?,?\s*",
            r"(?:^|\s)in base al contesto\s*(?:fornito|disponibile)?,?\s*",
            r"(?:^|\s)secondo il contesto\s*(?:fornito|disponibile)?,?\s*",
            r"(?:^|\s)dalla documentazione\s*(?:fornita|disponibile|in mio possesso)?,?\s*",
            r"(?:^|\s)basandomi sui documenti\s*(?:forniti|disponibili)?,?\s*",
            r"(?:^|\s)consultando i manuali\s*(?:forniti|disponibili)?,?\s*",
            # Pattern più specifico per "dai documenti" che non interferisce con i link
            r"(?:^|\s)dai documenti\s+(?:forniti|disponibili|in mio possesso)\s*,?\s*",
        ]

        cleaned_response = protected_response

        # Rimuovi i pattern problematici
        for pattern in patterns_to_remove:
            cleaned_response = re.sub(pattern, " ", cleaned_response, flags=re.IGNORECASE)

        # Ripristina i link originali
        for i, link in enumerate(links):
            placeholder = f"__LINK_PLACEHOLDER_{i}__"
            cleaned_response = cleaned_response.replace(placeholder, link)

        # Pulisci spazi multipli e virgole orfane
        cleaned_response = re.sub(r'\s+', ' ', cleaned_response)
        cleaned_response = re.sub(r',\s*,', ',', cleaned_response)
        cleaned_response = re.sub(r'^\s*,\s*', '', cleaned_response)
        cleaned_response = cleaned_response.strip()

        # Se la risposta inizia con una virgola o punto, rimuovila
        if cleaned_response.startswith((',', '.')):
            cleaned_response = cleaned_response[1:].strip()

        # Capitalizza la prima lettera se necessario
        if cleaned_response and cleaned_response[0].islower():
            cleaned_response = cleaned_response[0].upper() + cleaned_response[1:]

        logger.info(f"Cleaned response after cleaning: {cleaned_response}")
        return cleaned_response

    def search_and_answer(self, query: str, product_code: str, chat_history: List[Dict] = [], n_results: int = 7) -> Tuple[str, List[Dict], Dict]:
        if not self.collection:
            raise ValueError("Collection non pronta.")

        # Process input with guardrails
        input_result = self.guardrail_manager.process_input(query)
        logger.info(f"Input Guardrails Result: {input_result}")

        # Initialize guardrail data collection
        guardrail_data = {
            'input_guardrail': input_result,
            'output_guardrail': None
        }

        if input_result.get("blocked"):
            return input_result.get("reason", "Input blocked by guardrails."), chat_history, guardrail_data

        query = input_result.get("text", query)  # Use 'text' instead of 'modified_text'
        
        try:
            logger.info(f"Eseguo query per '{query[:30]}...' con filtro product_code='{product_code}'")
            search_results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where={"product_code": product_code}  # <-- FILTRO APPLICATO QUI
            )
            
            answer = self._generate_answer(query, search_results, chat_history)

            # Process output with guardrails
            output_result = self.guardrail_manager.process_output(answer)
            logger.info(f"Output Guardrails Result: {output_result}")

            # Store output guardrail data
            guardrail_data['output_guardrail'] = output_result

            if output_result.get("blocked"):
                answer = output_result.get("reason", "Output blocked by guardrails.")
            else:
                # Usa il campo 'text' che contiene sempre il testo (modificato o originale)
                answer = output_result.get("text", answer)

            # Aggiorna la cronologia
            updated_history = chat_history + [{"role": "user", "parts": [query]}, {"role": "model", "parts": [answer]}]

            return answer, updated_history, guardrail_data
        except Exception as e:
            logger.error(f"Errore durante la ricerca: {e}")
            return f"❌ Errore durante l'elaborazione della ricerca: {e}", chat_history, guardrail_data

    def run_product_chat(self, product_code: str):
        """Avvia una sessione di chat per un prodotto specifico."""
        print("-" * 60)
        print(f"🗣️  Chat attivata per il prodotto: **{product_code}**")
        print("💡 Fai le tue domande. Scrivi 'cambia' per selezionare un altro prodotto o 'quit' per uscire.")
        print("-" * 60)

        chat_history = []
        while True:
            query = input("\n🧑 Tu: ").strip()
            if not query: continue

            if query.lower() == 'quit':
                print("👋 Arrivederci!")
                sys.exit(0)
            
            if query.lower() == 'cambia':
                print("Ok, torniamo alla selezione del prodotto.")
                break
            
            print("🔍 Ricerca in corso...")
            response, chat_history, guardrail_data = self.search_and_answer(query, product_code, chat_history=chat_history)
            print(f"\n🤖 Bot:\n{response}")

    def start_chat_service(self):
        """Funzione principale che gestisce la selezione del prodotto e avvia la chat."""
        print("🤖 Benvenuto nel Chatbot per Documenti di Prodotto")
        print("=" * 60)
        
        while True:
            product_code = input("▶️ Inserisci il codice prodotto (es. CODICE_PRODOTTO_A) o 'quit' per uscire: ").strip()
            if not product_code: continue

            if product_code.lower() == 'quit':
                print("👋 Arrivederci!")
                break
            
            # Prepara i documenti per il prodotto richiesto
            documents_ready = self.prepare_product_documents(product_code)
            
            if documents_ready:
                # Se i documenti sono pronti, avvia la chat per quel prodotto
                self.run_product_chat(product_code)
            else:
                # Se non ci sono documenti, avvisa l'utente e chiedi un nuovo codice
                print(f"❌ Impossibile trovare documenti per il codice prodotto '{product_code}'. Controlla la cartella e riprova.")
            
            print("\n" + "=" * 60)


def main():
    """Punto di ingresso dello script."""
    load_dotenv()
    print("🚀 Avvio Product Chatbot...")
    
    # Controlla se la cartella 'pdf' esiste
    if not Path("pdf").is_dir():
        print("❌ Cartella 'pdf' non trovata. Creala e inserisci le sottocartelle per ogni prodotto.")
        print("Esempio: ./pdf/MIO_PRODOTTO/documento.pdf")
        sys.exit(1)

    force_reprocess = '--reprocess' in sys.argv or '-r' in sys.argv
    
    jina_api_key = os.getenv("JINA_API_KEY")
    gemini_api_key = os.getenv("GEMINI_API_KEY")

    if not jina_api_key or not gemini_api_key:
        print("❌ Errore: Imposta le variabili d'ambiente JINA_API_KEY e GEMINI_API_KEY.")
        sys.exit(1)

    try:
        chatbot = ProductChatbot(
            jina_api_key=jina_api_key, 
            gemini_api_key=gemini_api_key, 
            force_reprocess=force_reprocess
        )
        chatbot.start_chat_service()
    except ValueError as e:
        print(e)
        sys.exit(1)
    except Exception as e:
        print(f"❌ Si è verificato un errore imprevisto: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()