# Sistema di Modale Fullscreen per Link

## Panoramica

Il sistema di modale fullscreen sostituisce l'apertura dei link in nuove schede (`target="_blank"`) con una modale elegante e funzionale che si apre a schermo intero. Questo migliora l'esperienza utente mantenendo il contesto della conversazione del chatbot.

## Caratteristiche Implementate

### ✅ Funzionalità Core
- **Modale fullscreen**: Apertura dei link in una modale che occupa tutto lo schermo
- **Header con controlli**: Pulsanti per navigazione (indietro, avanti, ricarica) e chiusura
- **Iframe integrato**: Caricamento del contenuto del link all'interno della modale
- **Loader animato**: Indicatore di caricamento durante l'apertura dei link
- **Gestione errori**: Messaggi informativi per errori di caricamento con suggerimenti
- **Cronologia di navigazione**: Supporto per navigazione avanti/indietro all'interno della modale

### ✅ Controlli di Navigazione
- **Pulsante Indietro**: Torna alla pagina precedente nella cronologia della modale
- **Pulsante Avanti**: Vai alla pagina successiva (se disponibile)
- **Pulsante Ricarica**: Ricarica la pagina corrente
- **Pulsante Chiudi**: Chiude la modale e torna al chatbot

### ✅ Gestione Errori e Loader
- **Loader con animazione**: Spinner rotante con testo informativo
- **Timeout di caricamento**: 30 secondi di timeout per evitare attese infinite
- **Messaggi di errore specifici**: Diversi messaggi per diversi tipi di errore
- **Pulsante Riprova**: Possibilità di ritentare il caricamento
- **Validazione URL**: Controllo della validità degli URL prima del caricamento

### ✅ Accessibilità
- **Attributi ARIA**: Supporto completo per screen reader
- **Focus trap**: Il focus rimane all'interno della modale quando aperta
- **Gestione focus**: Ripristino del focus precedente alla chiusura
- **Tasti di scelta rapida**: 
  - `Esc`: Chiude la modale
  - `F5`: Ricarica la pagina
  - `Alt + ←`: Vai indietro
  - `Alt + →`: Vai avanti

### ✅ Design Responsive
- **Desktop**: Modale con dimensioni ottimali e controlli completi
- **Tablet**: Adattamento delle dimensioni e spaziature
- **Mobile**: Layout ottimizzato per schermi piccoli
- **Media queries**: Supporto per schermi da 480px a desktop

### ✅ Integrazione
- **Compatibilità**: Funziona con entrambe le versioni del chatbot (principale e embedded)
- **File condiviso**: Un unico file JavaScript (`link-modal.js`) per entrambe le versioni
- **Stili separati**: CSS specifici per ogni versione mantenendo coerenza

## Struttura dei File

```
static/
├── js/
│   ├── link-modal.js      # Logica principale della modale (condivisa)
│   ├── script.js          # Script principale del chatbot
│   └── embedded.js        # Script per versione embedded
├── css/
│   ├── style.css          # Stili per versione principale (con stili modale)
│   └── embedded.css       # Stili per versione embedded (con stili modale)
templates/
├── index.html             # Template principale (con HTML modale)
└── embedded.html          # Template embedded (con HTML modale)
```

## Come Funziona

### 1. Intercettazione dei Link
Il sistema intercetta automaticamente tutti i click sui link nei messaggi del chatbot:

```javascript
document.addEventListener('click', (e) => {
    if (e.target.tagName === 'A' && e.target.href) {
        if (this.shouldOpenInModal(url)) {
            e.preventDefault();
            this.openLink(e.target.href, e.target.textContent);
        }
    }
});
```

### 2. Logica di Apertura
La modale si apre per:
- File PDF
- Link dello stesso dominio
- Domini di documentazione comuni (GitHub, Stack Overflow, MDN, etc.)

### 3. Gestione della Cronologia
La modale mantiene una cronologia interna separata dalla cronologia del browser:

```javascript
// Aggiungi alla cronologia
this.history.push(url);
this.currentIndex = this.history.length - 1;
```

### 4. Gestione degli Errori
Sistema robusto di gestione errori con messaggi specifici:

```javascript
if (message.includes('Timeout')) {
    suggestions = 'La pagina potrebbe essere lenta o non disponibile.';
} else if (message.includes('404')) {
    suggestions = 'La pagina richiesta non esiste o è stata spostata.';
}
```

## Tasti di Scelta Rapida

| Tasto | Azione |
|-------|--------|
| `Esc` | Chiude la modale |
| `F5` | Ricarica la pagina corrente |
| `Alt + ←` | Vai alla pagina precedente |
| `Alt + →` | Vai alla pagina successiva |
| `Tab` | Naviga tra i controlli (con focus trap) |

## Personalizzazione

### Timeout di Caricamento
```javascript
this.maxLoadTime = 30000; // 30 secondi (modificabile)
```

### Domini per Apertura in Modale
```javascript
const docDomains = [
    'docs.google.com',
    'github.com',
    'gitlab.com',
    // Aggiungi altri domini qui
];
```

### Stili CSS
Gli stili sono completamente personalizzabili modificando le variabili CSS:

```css
.modal-container {
    width: 95%;
    height: 95%;
    max-width: 1400px;
    max-height: 900px;
}
```

## Compatibilità Browser

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Note Tecniche

### CORS e Sicurezza
- La modale rispetta le politiche CORS
- Non può accedere al contenuto di domini esterni per motivi di sicurezza
- I titoli delle pagine esterne potrebbero non essere disponibili

### Performance
- Caricamento lazy degli iframe
- Cleanup automatico delle risorse alla chiusura
- Gestione memoria ottimizzata

### Accessibilità
- Conforme alle linee guida WCAG 2.1
- Supporto completo per screen reader
- Navigazione da tastiera ottimizzata
- Focus management robusto

## Risoluzione Problemi

### La modale non si apre
1. Verifica che il JavaScript sia caricato correttamente
2. Controlla la console per errori
3. Assicurati che l'URL sia valido

### Errori di caricamento
1. Verifica la connessione internet
2. Controlla se l'URL è accessibile
3. Alcuni siti potrebbero bloccare l'embedding in iframe

### Problemi di responsive
1. Verifica le media queries CSS
2. Testa su diversi dispositivi
3. Controlla il viewport meta tag

## Manutenzione

### Aggiornamenti Futuri
- Monitorare le performance di caricamento
- Aggiornare la lista dei domini supportati
- Migliorare la gestione errori basata sui feedback utenti

### Log e Debug
Il sistema include logging dettagliato per il debug:
```javascript
console.log('CORS restriction - cannot access iframe content');
```
