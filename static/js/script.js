document.addEventListener('DOMContentLoaded', () => {
    // Riferimenti agli elementi del DOM
    const chatBox = document.getElementById('chat-box');
    const chatForm = document.getElementById('chat-form');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const modal = document.getElementById('product-modal');
    const productForm = document.getElementById('product-form');
    const productCodeInput = document.getElementById('product-code-input');
    const modalStatus = document.getElementById('modal-status');
    const menuBtn = document.getElementById('menu-btn');
    const dropdownMenu = document.getElementById('dropdown-menu');
    const clearChatBtn = document.getElementById('clear-chat-btn');
    const restartChatBtn = document.getElementById('restart-chat-btn');

    let currentProductCode = null;
    let chatHistory = []; // Aggiungi questa linea per la cronologia

    // --- Funzioni di gestione della UI ---

    // Mostra/Nasconde il menu a tendina
    menuBtn.addEventListener('click', () => {
        dropdownMenu.classList.toggle('show');
    });

    // Chiude il menu se si clicca fuori
    window.addEventListener('click', (e) => {
        if (!menuBtn.contains(e.target)) {
            dropdownMenu.classList.remove('show');
        }
    });

    // Funzione per svuotare la chat
    clearChatBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        chatBox.innerHTML = ''; // Svuota la chat box
        chatHistory = []; // Svuota anche la cronologia
        addMessage(`Chat cancellata. Pronto per una nuova domanda su <strong>${currentProductCode}</strong>.`, 'bot');
        dropdownMenu.classList.remove('show');
    });

    // Funzione per riavviare la chat
    restartChatBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        // Resetta lo stato
        currentProductCode = null;
        chatHistory = []; // Resetta anche la cronologia
        chatBox.innerHTML = '';
        userInput.value = '';
        userInput.disabled = true;
        sendBtn.disabled = true;
        productCodeInput.value = '';
        modalStatus.textContent = '';
        modalStatus.className = 'modal-status';
        
        // Mostra di nuovo il modale
        modal.style.display = 'flex';
        productCodeInput.focus();
        dropdownMenu.classList.remove('show');
    });


    // Aggiunge un messaggio alla chat-box
    function addMessage(text, sender) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message', sender);

        // Crea un elemento temporaneo per manipolare l'HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;

        // I link verranno gestiti dalla modale fullscreen
        // Non è più necessario aggiungere target="_blank"

        messageElement.innerHTML = tempDiv.innerHTML; // Usa l'HTML modificato
        chatBox.appendChild(messageElement);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Gestisce l'invio del codice prodotto
    productForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const productCode = productCodeInput.value.trim();
        if (!productCode) return;

        modalStatus.textContent = 'Preparazione dei documenti in corso...';
        modalStatus.className = 'modal-status';

        try {
            const response = await fetch('/prepare', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ product_code: productCode }),
            });

            const data = await response.json();

            if (data.success) {
                currentProductCode = productCode;
                modalStatus.textContent = `Documenti pronti per ${productCode}!`;
                modalStatus.classList.add('success');
                setTimeout(() => {
                    modal.style.display = 'none';
                    userInput.disabled = false;
                    sendBtn.disabled = false;
                    userInput.focus();
                    addMessage(`Ciao! Sono pronto a rispondere a domande sui documenti del prodotto <strong>${currentProductCode}</strong>.`, 'bot');
                }, 1500);
            } else {
                modalStatus.textContent = data.message;
                modalStatus.classList.add('error');
            }
        } catch (error) {
            modalStatus.textContent = 'Errore di connessione con il server.';
            modalStatus.classList.add('error');
        }
    });

    // Gestisce l'invio di un messaggio di chat
    chatForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const userMessage = userInput.value.trim();
        if (!userMessage || !currentProductCode) return;

        addMessage(userMessage, 'user');
        userInput.value = '';
        userInput.disabled = true;
        sendBtn.disabled = true;
        
        // Messaggio di attesa del bot
        addMessage('Sto pensando...', 'bot');

        try {
            const response = await fetch('/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    message: userMessage, 
                    product_code: currentProductCode,
                    history: chatHistory // Invia la cronologia
                }),
            });

            const data = await response.json();
            
            // Aggiorna la cronologia con la risposta del bot
            chatHistory = data.history;

            // Rimuove il messaggio "Sto pensando..."
            chatBox.removeChild(chatBox.lastChild);
            
            // Aggiunge la risposta vera e propria
            addMessage(data.answer, 'bot');

        } catch (error) {
            chatBox.removeChild(chatBox.lastChild);
            addMessage('Si è verificato un errore di connessione.', 'bot');
        } finally {
            userInput.disabled = false;
            sendBtn.disabled = false;
            userInput.focus();
        }
    });
});