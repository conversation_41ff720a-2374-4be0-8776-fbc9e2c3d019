# 🤖 Sistema di Embedding Automatizzato - Guida Completa

## 📋 Panoramica

Ho creato un sistema completo di embedding automatizzato per la tua knowledge base che include:

- **Scansione ricorsiva** di tutti i documenti in `pdf/`
- **Modalità incrementale** che processa solo file nuovi/modificati
- **Progress bar e logging** dettagliato
- **Report statistici** completi
- **Scheduling automatico** con configurazione flessibile
- **Esecuzione in background** e supporto daemon
- **Gestione errori robusta** con retry automatico

## 🚀 Installazione Rapida

### 1. Setup Automatico
```bash
# Installa e configura tutto automaticamente
python scripts/setup_embedding_system.py

# Solo verifica configurazione
python scripts/setup_embedding_system.py --check-only
```

### 2. Configura API Keys
```bash
# Copia il file di esempio
cp .env.example .env

# Modifica con le tue API keys
nano .env
```

Aggiungi le tue chiavi:
```env
JINA_API_KEY=your_jina_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Test del Sistema
```bash
# Test rapidi
python scripts/test_embedding_system.py --quick

# Test completi
python scripts/test_embedding_system.py --full
```

## 📖 Utilizzo

### Manager Unificato (Raccomandato)

```bash
# Mostra stato del sistema
python scripts/embedding_manager.py status

# Esegue embedding una volta
python scripts/embedding_manager.py run

# Embedding con riprocessamento forzato
python scripts/embedding_manager.py run --force-reprocess

# Embedding di un prodotto specifico
python scripts/embedding_manager.py run --product-code CODICE_PRODOTTO_A

# Simulazione (dry run)
python scripts/embedding_manager.py run --dry-run
```

### Scheduling Automatico

```bash
# Avvia scheduler ogni ora
python scripts/embedding_manager.py schedule start --interval 3600

# Avvia scheduler con orario specifico (2:00 AM)
python scripts/embedding_manager.py schedule start --schedule-time 02:00 --daemon

# Mostra stato scheduler
python scripts/embedding_manager.py schedule status

# Ferma scheduler
python scripts/embedding_manager.py schedule stop
```

### Gestione Report

```bash
# Lista report recenti
python scripts/embedding_manager.py reports list --last 5

# Mostra report specifico
python scripts/embedding_manager.py reports show embedding_report_20241221_143022.json

# Pulisce report vecchi
python scripts/embedding_manager.py reports cleanup --older-than 30
```

## 📁 Struttura File Creata

```
scripts/
├── embedding_config.py          # Configurazioni centrali
├── embedding_utils.py           # Utilità condivise  
├── automated_embedding.py       # Script principale embedding
├── embedding_scheduler.py       # Sistema scheduling
├── embedding_manager.py         # Manager unificato
├── test_embedding_system.py     # Test del sistema
├── setup_embedding_system.py    # Setup automatico
└── README.md                    # Documentazione dettagliata

logs/                            # Log del sistema
reports/                         # Report di elaborazione
chromadb_data/                   # Database vettoriale
schedule_config.json             # Configurazione scheduling
.env.example                     # Template variabili ambiente
```

## ⚙️ Configurazioni Principali

### Parametri Embedding (in `scripts/embedding_config.py`)
```python
CHUNK_SIZE = 1000              # Dimensione chunk testo
CHUNK_OVERLAP = 200            # Sovrapposizione chunk
BATCH_SIZE = 50                # Batch per ChromaDB
MAX_RETRIES = 3                # Tentativi API
```

### Estensioni Supportate
- PDF (`.pdf`)
- Testo (`.txt`) 
- Markdown (`.md`)
- CSV (`.csv`)
- Word (`.doc`, `.docx`) - *in sviluppo*

### Scheduling (in `schedule_config.json`)
```json
{
  "enabled": true,
  "interval_seconds": 3600,
  "scheduled_times": ["02:00"],
  "max_concurrent_jobs": 1,
  "retry_failed_jobs": true,
  "embedding_options": {
    "background": true,
    "force_reprocess": false
  }
}
```

## 📊 Monitoraggio

### Log
- `logs/automated_embedding.log` - Log embedding
- `logs/embedding_scheduler.log` - Log scheduler  
- `logs/embedding_manager.log` - Log manager

### Report
I report in `reports/` includono:
- Statistiche elaborazione (file processati, errori, tempi)
- Performance sistema (memoria, CPU, API calls)
- Configurazione utilizzata
- Informazioni sistema

Esempio report:
```json
{
  "processing_stats": {
    "total_files": 150,
    "processed_files": 148, 
    "failed_files": 2,
    "success_rate": 98.7,
    "processing_time_seconds": 1847.5
  },
  "performance_stats": {
    "memory_usage_mb": 245.8,
    "api_calls": 108
  }
}
```

## 🔄 Modalità Incrementale

Il sistema traccia automaticamente i file usando hash MD5:

1. **Prima esecuzione**: Processa tutti i file
2. **Esecuzioni successive**: Solo file nuovi/modificati  
3. **Force reprocess**: Forza riprocessamento completo

Gli hash sono salvati in `pdf_metadata.json`.

## 🕐 Esecuzione come Servizio

### Linux (systemd)
```bash
# Crea file servizio
sudo nano /etc/systemd/system/embedding-scheduler.service
```

```ini
[Unit]
Description=Embedding Scheduler
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/python3 scripts/embedding_scheduler.py --daemon
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Abilita e avvia
sudo systemctl enable embedding-scheduler
sudo systemctl start embedding-scheduler
```

## 🛠️ Troubleshooting

### Problemi Comuni

1. **API Keys mancanti**
   ```
   ❌ JINA_API_KEY non trovata
   ```
   **Soluzione**: Configura `.env` con le tue API keys

2. **Directory PDF non trovata**
   ```
   ❌ Directory PDF non trovata: pdf
   ```
   **Soluzione**: Crea directory `pdf/` nella root

3. **Errori memoria**
   **Soluzione**: Riduci `BATCH_SIZE` in configurazione

4. **Errori rete/API**
   **Soluzione**: Aumenta `MAX_RETRIES`, verifica connessione

### Debug
```bash
# Debug dettagliato
python scripts/automated_embedding.py --verbose

# Verifica stato sistema
python scripts/embedding_manager.py status

# Test componenti
python scripts/test_embedding_system.py --quick
```

### Pulizia Sistema
```bash
# Pulisce file vecchi (log, report)
python scripts/embedding_manager.py cleanup --older-than 30
```

## 📈 Esempi di Utilizzo

### Scenario 1: Embedding Manuale
```bash
# Processa tutti i documenti una volta
python scripts/embedding_manager.py run --verbose
```

### Scenario 2: Scheduling Automatico
```bash
# Setup scheduling ogni 2 ore
python scripts/embedding_manager.py schedule start --interval 7200 --daemon

# Verifica stato
python scripts/embedding_manager.py schedule status
```

### Scenario 3: Prodotto Specifico
```bash
# Processa solo un prodotto
python scripts/embedding_manager.py run --product-code CODICE_PRODOTTO_A --force-reprocess
```

### Scenario 4: Monitoraggio
```bash
# Stato generale
python scripts/embedding_manager.py status

# Report recenti  
python scripts/embedding_manager.py reports list --last 10
```

## 🔮 Funzionalità Avanzate

### Configurazione Personalizzata
Modifica `scripts/embedding_config.py` per:
- Cambiare dimensioni chunk
- Aggiungere nuove estensioni
- Modificare parametri API
- Personalizzare logging

### Estensioni Future
Il sistema è progettato per supportare:
- [ ] Documenti Word (.doc, .docx)
- [ ] Elaborazione immagini con OCR
- [ ] Interfaccia web monitoraggio
- [ ] Notifiche email errori
- [ ] Backup automatico database

## ✅ Checklist Deployment

- [ ] Eseguito setup: `python scripts/setup_embedding_system.py`
- [ ] Configurate API keys in `.env`
- [ ] Test superati: `python scripts/test_embedding_system.py --quick`
- [ ] Documenti aggiunti in `pdf/`
- [ ] Primo embedding: `python scripts/embedding_manager.py run`
- [ ] Scheduling configurato (opzionale)
- [ ] Monitoraggio attivo

## 📞 Supporto

1. **Verifica stato**: `python scripts/embedding_manager.py status`
2. **Controlla log**: `logs/` directory
3. **Esegui test**: `python scripts/test_embedding_system.py`
4. **Consulta documentazione**: `scripts/README.md`

Il sistema è ora pronto per l'uso in produzione! 🚀
