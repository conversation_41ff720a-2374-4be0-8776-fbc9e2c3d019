# Sistema di Logging delle Conversazioni

Questo documento descrive il sistema di logging delle conversazioni implementato per l'applicazione chat-jina.

## Panoramica

Il sistema di logging delle conversazioni è progettato per registrare automaticamente tutte le interazioni tra gli utenti e il chatbot in un database MySQL. Il sistema include:

- Middleware per catturare automaticamente le interazioni
- Gestione asincrona del salvataggio per non rallentare le risposte
- Meccanismo di retry per le scritture fallite
- API per query e statistiche sulle conversazioni

## Schema del Database

```sql
CREATE TABLE conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    product VARCHAR(255),
    question TEXT NOT NULL,
    response TEXT NOT NULL,
    guardrail_log JSON,
    session_id VARCHAR(255),
    user_agent TEXT,
    response_time_ms INT,
    confidence_score FLOAT,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip (ip_address),
    INDEX idx_product (product),
    INDEX idx_session (session_id)
);
```

### Campo guardrail_log

Il campo `guardrail_log` è di tipo JSON e contiene solo i risultati dei guardrail (`guardrail_results`) combinati da input e output. Esempio di struttura:

```json
[
  {
    "action": "ALLOW",
    "details": {
      "metrics": {
        "characters": 32,
        "words": 5
      }
    }
  },
  {
    "action": "ALLOW",
    "details": {
      "detected_language": "it",
      "confidence": 0.5714285714285714
    }
  },
  {
    "action": "ALLOW"
  },
  {
    "action": "ALLOW"
  },
  {
    "action": "ALLOW"
  }
]
```

Questo formato contiene solo le informazioni essenziali sui controlli dei guardrail, combinando i risultati sia dell'input che dell'output processing.

## Configurazione

La configurazione del database viene gestita tramite variabili d'ambiente nel file `.env`:

```
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=chat_jina
DB_USER=prova
DB_PASSWORD=prova
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
```

## Installazione

1. Assicurati che MySQL sia installato e in esecuzione sul tuo sistema
2. Aggiungi le variabili di configurazione del database al file `.env`
3. Installa le dipendenze richieste:
   ```
   pip install mysql-connector-python asyncio aiofiles
   ```
4. Esegui lo script di setup per creare il database e testare il sistema:
   ```
   python setup_logging.py
   ```

## Architettura

Il sistema di logging è composto da diversi moduli:

### 1. Database Manager (`database/db_manager.py`)

Gestisce le connessioni al database e le operazioni CRUD:
- Connection pooling per ottimizzare le prestazioni
- Gestione degli errori e delle eccezioni
- Metodi per inserire, recuperare e analizzare i dati delle conversazioni

### 2. Conversation Logger (`logging_service/conversation_logger.py`)

Gestisce il logging asincrono delle conversazioni:
- Code per il buffering delle richieste di logging
- Worker thread per l'elaborazione asincrona
- Meccanismo di retry per gestire i fallimenti temporanei
- Shutdown graceful per evitare la perdita di dati

### 3. Middleware Flask (`middleware/conversation_middleware.py`)

Intercetta automaticamente le richieste HTTP:
- Cattura i dettagli delle richieste e delle risposte
- Estrae informazioni rilevanti dalle conversazioni
- Invia i dati al Conversation Logger in modo asincrono

### 4. API per Statistiche (`api/conversation_api.py`)

Fornisce endpoint per interrogare e analizzare i dati delle conversazioni:
- `/api/conversations/stats` - Statistiche generali
- `/api/conversations/search` - Ricerca conversazioni
- `/api/conversations/daily-stats` - Statistiche giornaliere
- `/api/conversations/products` - Statistiche per prodotto
- `/api/conversations/health` - Health check del sistema

## Utilizzo

### Avvio dell'Applicazione

Ci sono due modi per avviare l'applicazione:

1. **Metodo standard**:
   ```bash
   python app.py
   ```

2. **Metodo alternativo** (consigliato se hai problemi con Ctrl+C):
   ```bash
   python run_app.py
   ```

### Arresto dell'Applicazione

- **Ctrl+C**: Dovrebbe funzionare normalmente per arrestare l'applicazione
- **Script di arresto**: Se Ctrl+C non funziona, usa:
  ```bash
  python stop_app.py
  ```

### Logging Automatico

Il sistema registra automaticamente tutte le interazioni con l'endpoint `/chat`. Non è necessaria alcuna modifica al codice esistente per abilitare questa funzionalità.

### Query e Statistiche

Puoi utilizzare gli endpoint API per ottenere statistiche e cercare conversazioni:

```python
import requests

# Ottieni statistiche generali
response = requests.get('http://localhost:5001/api/conversations/stats')
stats = response.json()

# Cerca conversazioni per un prodotto specifico
response = requests.get('http://localhost:5001/api/conversations/search?product=CODICE_A&limit=10')
conversations = response.json()

# Ottieni statistiche giornaliere degli ultimi 7 giorni
response = requests.get('http://localhost:5001/api/conversations/daily-stats?days=7')
daily_stats = response.json()
```

## Monitoraggio e Manutenzione

### Health Check

Puoi verificare lo stato del sistema di logging utilizzando l'endpoint di health check:

```
GET /api/conversations/health
```

Questo endpoint restituisce informazioni sullo stato del database e delle code di logging.

### Gestione delle Code

Il sistema utilizza code in memoria per il buffering delle richieste di logging. In caso di problemi, puoi verificare lo stato delle code tramite l'endpoint di health check.

### Backup del Database

Si consiglia di configurare backup regolari del database MySQL per evitare la perdita di dati. Esempio di comando per il backup:

```bash
mysqldump -u prova -p chat_jina > chat_jina_backup_$(date +%Y%m%d).sql
```

## Estensioni Future

Il sistema di logging può essere esteso in vari modi:

1. **Dashboard di Analisi**: Creare un'interfaccia web per visualizzare le statistiche
2. **Esportazione Dati**: Aggiungere funzionalità per esportare i dati in CSV o JSON
3. **Alerting**: Implementare notifiche per pattern anomali nelle conversazioni
4. **Integrazione con Sistemi di Monitoraggio**: Esporre metriche per Prometheus o altri sistemi

## Risoluzione dei Problemi

### Problemi di Connessione al Database

Se l'applicazione non riesce a connettersi al database:

1. Verifica che MySQL sia in esecuzione
2. Controlla le credenziali nel file `.env`
3. Assicurati che l'utente abbia i permessi necessari

### Problemi di Performance

Se noti rallentamenti:

1. Aumenta `DB_POOL_SIZE` nel file `.env`
2. Verifica che gli indici del database siano utilizzati correttamente
3. Considera l'aggiunta di indici aggiuntivi in base ai pattern di query

### Errori di Logging

Se le conversazioni non vengono registrate:

1. Controlla i log dell'applicazione per errori
2. Verifica lo stato del sistema con l'endpoint di health check
3. Controlla lo spazio disponibile sul disco per il database
