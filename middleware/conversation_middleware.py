"""
Flask middleware for automatic conversation logging.
"""

import time
import uuid
import json
import logging
from datetime import datetime
from flask import request, g
from functools import wraps
from typing import Dict, Any, Optional
from logging_service import conversation_logger

logger = logging.getLogger(__name__)


class ConversationMiddleware:
    """Middleware for automatic conversation logging in Flask."""
    
    def __init__(self, app=None):
        """
        Initialize the middleware.
        
        Args:
            app: Flask application instance
        """
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the middleware with Flask app."""
        self.app = app
        
        # Register before and after request handlers
        app.before_request(self._before_request)
        app.after_request(self._after_request)
        
        logger.info("ConversationMiddleware initialized")
    
    def _before_request(self):
        """Hand<PERSON> called before each request."""
        # Generate session ID if not present
        if not hasattr(g, 'session_id'):
            g.session_id = str(uuid.uuid4())
        
        # Record request start time
        g.request_start_time = time.time()
        
        # Store request data for logging
        g.request_data = {
            'ip_address': self._get_client_ip(),
            'user_agent': request.headers.get('User-Agent', ''),
            'session_id': g.session_id,
            'timestamp': datetime.utcnow(),
            'endpoint': request.endpoint,
            'method': request.method,
            'url': request.url
        }
    
    def _after_request(self, response):
        """Handler called after each request."""
        # Calculate response time
        if hasattr(g, 'request_start_time'):
            response_time_ms = int((time.time() - g.request_start_time) * 1000)
        else:
            response_time_ms = 0
        
        # Only log chat interactions
        if request.endpoint == 'chat' and request.method == 'POST':
            self._log_chat_interaction(response, response_time_ms)
        
        return response
    
    def _log_chat_interaction(self, response, response_time_ms: int):
        """Log chat interaction to database."""
        try:
            # Get request data
            request_data = getattr(g, 'request_data', {})
            
            # Parse request JSON
            request_json = request.get_json() or {}
            user_message = request_json.get('message', '')
            product_code = request_json.get('product_code', '')
            
            # Parse response JSON
            response_json = {}
            if response.is_json:
                try:
                    response_json = response.get_json()
                except Exception as e:
                    logger.warning(f"Failed to parse response JSON: {e}")
            
            bot_response = response_json.get('answer', '')
            
            # Extract guardrail information from Flask's g object if available
            guardrail_log = self._extract_guardrail_info_from_g()
            
            # Calculate confidence score (placeholder - implement based on your needs)
            confidence_score = self._calculate_confidence_score(response_json)
            
            # Prepare conversation data
            conversation_data = {
                'ip_address': request_data.get('ip_address'),
                'product': product_code,
                'question': user_message,
                'response': bot_response,
                'guardrail_log': guardrail_log,
                'session_id': request_data.get('session_id'),
                'user_agent': request_data.get('user_agent'),
                'response_time_ms': response_time_ms,
                'confidence_score': confidence_score
            }
            
            # Log asynchronously
            success = conversation_logger.log_conversation_async(conversation_data)
            
            if success:
                logger.debug(f"Chat interaction queued for logging: session={request_data.get('session_id')}")
            else:
                logger.warning(f"Failed to queue chat interaction for logging: session={request_data.get('session_id')}")
                
        except Exception as e:
            logger.error(f"Error in conversation logging middleware: {e}")
    
    def _get_client_ip(self) -> str:
        """Get client IP address, considering proxies."""
        # Check for forwarded IP first
        forwarded_ips = request.headers.get('X-Forwarded-For')
        if forwarded_ips:
            # Take the first IP in the chain
            return forwarded_ips.split(',')[0].strip()
        
        # Check for real IP header
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fall back to remote address
        return request.remote_addr or 'unknown'
    
    def _extract_guardrail_info_from_g(self) -> Optional[List]:
        """Extract only guardrail_results from Flask's g object."""
        try:
            if hasattr(g, 'guardrail_data') and g.guardrail_data:
                guardrail_data = g.guardrail_data

                # Extract only guardrail_results from both input and output
                all_guardrail_results = []

                # Get input guardrail results
                input_guardrail = guardrail_data.get('input_guardrail', {})
                input_metadata = input_guardrail.get('metadata', {})
                input_results = input_metadata.get('guardrail_results', [])

                # Get output guardrail results
                output_guardrail = guardrail_data.get('output_guardrail', {})
                output_metadata = output_guardrail.get('metadata', {})
                output_results = output_metadata.get('guardrail_results', [])

                # Combine all results
                all_guardrail_results.extend(input_results)
                all_guardrail_results.extend(output_results)

                return all_guardrail_results if all_guardrail_results else None

        except Exception as e:
            logger.warning(f"Error extracting guardrail data from g object: {e}")

        return None

    def _extract_guardrail_info(self, request_json: Dict, response_json: Dict) -> Optional[List]:
        """Extract guardrail information from request/response (legacy method)."""
        # This is a fallback method - the main method now uses Flask's g object
        guardrail_results = []

        # Check if there are any guardrail-related fields in the response
        if 'guardrail_warnings' in response_json:
            guardrail_results.append({"action": "WARN", "details": {"warnings": response_json['guardrail_warnings']}})

        if 'guardrail_blocks' in response_json:
            guardrail_results.append({"action": "BLOCK", "details": {"blocks": response_json['guardrail_blocks']}})

        return guardrail_results if guardrail_results else None
    
    def _calculate_confidence_score(self, response_json: Dict) -> Optional[float]:
        """Calculate confidence score for the response."""
        # This is a placeholder - implement based on your confidence calculation logic
        # You might want to integrate with your AI model's confidence metrics
        
        # Example: Check if response contains error messages
        response_text = response_json.get('answer', '')
        
        if 'errore' in response_text.lower() or 'error' in response_text.lower():
            return 0.1
        elif 'non so' in response_text.lower() or "don't know" in response_text.lower():
            return 0.3
        elif len(response_text) < 50:
            return 0.6
        else:
            return 0.8
    
    def get_status(self) -> Dict[str, Any]:
        """Get middleware status information."""
        return {
            'middleware_active': True,
            'logger_queue_status': conversation_logger.get_queue_status()
        }


def log_conversation_decorator(f):
    """
    Decorator for manual conversation logging.
    Use this for endpoints that need custom logging logic.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Store start time
        start_time = time.time()
        
        # Execute the original function
        result = f(*args, **kwargs)
        
        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Add custom logging logic here if needed
        logger.debug(f"Function {f.__name__} executed in {response_time_ms}ms")
        
        return result
    
    return decorated_function
