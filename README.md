# Assistente Virtuale per Documentazione di Prodotto

Questo progetto implementa un chatbot basato su AI in grado di rispondere a domande degli utenti basandosi su una serie di documenti PDF specifici per un prodotto. Utilizza Jina AI per la generazione di embeddings, Google Gemini per la creazione di risposte conversazionali e ChromaDB per l'archiviazione e la ricerca vettoriale.

L'applicazione offre due interfacce utente: una chat a pagina intera e un widget di chat "embedded" progettato per essere facilmente integrato in siti web di terze parti.

## Indice

- [Caratteristiche Principali](#caratteristiche-principali)
- [Come Funziona](#come-funziona)
- [Struttura del Progetto](#struttura-del-progetto)
- [Installazione e Configurazione](#installazione-e-configurazione)
- [Utilizzo](#utilizzo)
- [Integrazione del Widget Embedded](#integrazione-del-widget-embedded)
- [Configurazione Avanzata](#configurazione-avanzata)
- [Endpoint API](#endpoint-api)
- [Correzione Bug: Link ai Documenti Rimossi dalle Risposte](#correzione-bug-link-ai-documenti-rimossi-dalle-risposte)

## Caratteristiche Principali

- **Elaborazione Dinamica dei PDF**: Indicizza automaticamente i documenti PDF associati a un codice prodotto.
- **Ricerca Semantica**: Utilizza embeddings vettoriali per trovare le informazioni più pertinenti all'interno dei documenti.
- **Risposte Contestuali**: Sfrutta la cronologia della conversazione per interpretare meglio l'intento dell'utente e fornire risposte più accurate.
- **Verbosità Configurabile**: Permette di impostare il livello di dettaglio delle risposte del chatbot (da 1 a 5).
- **Riferimenti ai Documenti**: Le risposte includono link diretti alle pagine dei PDF da cui sono state estratte le informazioni.
- **Doppia Interfaccia**: Offre sia una versione a pagina intera che un widget "embedded" per la massima flessibilità.
- **Caching Intelligente**: Rielabora solo i file PDF che sono stati modificati, per ottimizzare i tempi di avvio.

## Come Funziona

L'architettura del sistema si basa su un backend Python che utilizza il micro-framework Flask e un frontend HTML, CSS e JavaScript.

1.  **Backend (Python/Flask)**:
    -   `app.py`: Il file principale che gestisce le rotte web, le richieste API e l'inizializzazione del chatbot.
    -   `pdf_chatbot_prodotti.py`: Contiene la logica di base del chatbot (`ProductPDFChatbot`). Questa classe si occupa di:
        -   **Elaborazione dei PDF**: Estrarre il testo dai file PDF.
        -   **Creazione di Embeddings**: Convertire i chunk di testo in vettori numerici utilizzando l'API di Jina.
        -   **Indicizzazione**: Salvare i vettori e i metadati associati in un database ChromaDB.
        -   **Ricerca**: Eseguire ricerche di similarità per trovare i chunk di testo più rilevanti per una data domanda.
        -   **Generazione Risposta**: Inviare la domanda, la cronologia della chat e il contesto recuperato al modello Gemini per generare una risposta in linguaggio naturale.

2.  **Frontend (HTML/CSS/JS)**:
    -   `templates/index.html`: Template per la chat a pagina intera.
    -   `templates/embedded.html`: Template per il widget di chat.
    -   `static/`: Contiene i file CSS e JavaScript che gestiscono lo stile e l'interattività delle interfacce.

## Struttura del Progetto

```
/
├── app.py                  # Applicazione principale Flask
├── pdf_chatbot_prodotti.py # Logica del chatbot
├── requirements.txt        # Dipendenze Python
├── .env                    # File per le variabili d'ambiente (da creare)
├── .gitignore              # File ignorati da Git
├── README.md               # Questo file
├── pdf/                    # Cartella contenente i PDF dei prodotti
│   └── CODICE_PRODOTTO_A/
│       └── manuale.pdf
├── chromadb_data/          # Database vettoriale di ChromaDB
├── guardrails_py/          # Moduli di sicurezza per il chatbot
│   ├── __init__.py
│   ├── guardrail_manager.py
│   ├── input/
│   │   ├── __init__.py
│   │   ├── input_length_control_guardrail.py
│   │   ├── language_detection_guardrail.py
│   │   └── pii_filter_guardrail.py
│   └── output/
│       ├── __init__.py
│       ├── harmful_content_filter_guardrail.py
│       └── bias_fairness_mitigation_guardrail.py
├── static/                 # File statici (CSS, JS)
│   ├── css/
│   │   ├── style.css
│   │   └── embedded.css
│   └── js/
│       ├── script.js
│       └── embedded.js
└── templates/              # Template HTML di Flask
    ├── index.html
    └── embedded.html
```

## Installazione e Configurazione

Segui questi passaggi per eseguire il progetto in locale.

**Prerequisiti**:
- Python 3.8+
- `pip` (gestore di pacchetti Python)

**1. Clona il Repository**
```bash
git clone <URL_DEL_TUO_REPOSITORY>
cd <NOME_DELLA_CARTELLA>
```

**2. Crea un Ambiente Virtuale**
È consigliabile utilizzare un ambiente virtuale per isolare le dipendenze del progetto.
```bash
python -m venv env
source env/bin/activate  # Su Windows: env\Scripts\activate
```

**3. Installa le Dipendenze**
```bash
pip install -r requirements.txt
```

**4. Configura le Variabili d'Ambiente**
Crea un file chiamato `.env` nella root del progetto e aggiungi le tue API key e le configurazioni desiderate.

```dotenv
# .env
JINA_API_KEY="tua_api_key_jina"
GEMINI_API_KEY="tua_api_key_gemini"
VERBOSITY_LEVEL=3
```

- `JINA_API_KEY`: La tua chiave API per Jina AI.
- `GEMINI_API_KEY`: La tua chiave API per Google Gemini.
- `VERBOSITY_LEVEL`: Il livello di dettaglio delle risposte (da 1 a 5).

**5. Aggiungi i Documenti PDF**
Crea una sottocartella all'interno della cartella `pdf/` per ogni codice prodotto. Il nome della sottocartella deve corrispondere esattamente al codice prodotto che userai nell'interfaccia.

Esempio:
- `pdf/CODICE_A/manuale1.pdf`
- `pdf/CODICE_A/manuale2.pdf`
- `pdf/CODICE_B/specifiche.pdf`

## Utilizzo

Una volta completata la configurazione, avvia l'applicazione Flask:

```bash
python app.py
```

L'applicazione sarà accessibile ai seguenti indirizzi:
- **Chat a Pagina Intera**: `http://127.0.0.1:5000/`
- **Widget Embedded**: `http://127.0.0.1:5000/embedded`

Al primo avvio per un nuovo codice prodotto, il sistema richiederà del tempo per elaborare e indicizzare i documenti PDF. Le esecuzioni successive saranno molto più veloci.

## Integrazione del Widget Embedded

Per integrare il chatbot in una qualsiasi pagina web esterna, aggiungi il seguente codice HTML. Puoi personalizzare le dimensioni (`width` e `height`) secondo le tue necessità.

```html
<iframe 
    src="http://127.0.0.1:5000/embedded" 
    width="400" 
    height="600" 
    style="border:none; position:fixed; bottom:20px; right:20px; z-index: 9999;"
    title="Assistente Virtuale">
</iframe>
```

Questo `<iframe>` mostrerà il pulsante di avvio del chatbot nell'angolo in basso a destra della pagina ospitante.

## Guardrails

Il sistema integra una serie di "guardrails" per garantire la sicurezza, l'affidabilità e l'eticità delle interazioni del chatbot. Questi moduli, implementati in Python e situati nella cartella `guardrails_py`, analizzano sia l'input dell'utente che l'output del modello AI per prevenire abusi e contenuti inappropriati.

### Fix e Ottimizzazioni del Sistema Guardrails

Il sistema guardrails è stato recentemente ottimizzato per migliorare le performance e ridurre le dipendenze esterne.

**Problemi Risolti:**
-   **Dipendenze Mancanti**: Il sistema falliva a causa di dipendenze esterne pesanti (`langdetect`, `presidio_analyzer`, `presidio_anonymizer`, `spacy`). Queste sono state sostituite con implementazioni leggere basate su regex.
-   **Struttura del Pacchetto Mancante**: I file `__init__.py` mancanti impedivano l'importazione corretta dei pacchetti Python. Sono stati creati i file necessari per una struttura di pacchetto adeguata (`guardrails_py/__init__.py`, `guardrails_py/input/__init__.py`, `guardrails_py/output/__init__.py`).

**Ottimizzazioni Implementate:**
-   **Sostituzione Rilevamento Linguaggio**: Invece della libreria `langdetect`, è stata implementata una soluzione leggera basata su regex per il rilevamento della lingua (italiano e inglese), che supporta il punteggio di confidenza e non ha dipendenze esterne, risultando in una elaborazione più veloce.
-   **Sostituzione Rilevamento PII**: Invece di Presidio con il modello spaCy, è stata implementata una rilevazione PII completa basata su regex per indirizzi email, numeri di telefono (italiani e internazionali), numeri di carte di credito, codici fiscali italiani, partite IVA italiane, IBAN e indirizzi IP. Questa soluzione è più veloce e non ha dipendenze esterne.

**Benefici in Termini di Performance:**
-   **Dipendenze Ridotte**: Eliminate 3 pacchetti esterni pesanti.
-   **Avvio più Veloce**: Non è necessario caricare modelli spaCy.
-   **Minore Impronta di Memoria**: L'elaborazione basata su regex utilizza meno memoria.
-   **Maggiore Affidabilità**: Nessuna dipendenza da file di modelli esterni.
-   **Deployment più Semplice**: Meno dipendenze da gestire.

Tutti i guardrail esistenti continuano a funzionare correttamente.

### Guardrails di Input

Questi guardrail processano la richiesta dell'utente prima che venga inviata al modello AI.

-   **Input Length Control**: Controlla la lunghezza dell'input per prevenire attacchi di tipo Denial of Service (DoS) e ottimizzare le performance. Respinge o tronca i messaggi eccessivamente lunghi.
-   **Language Detection**: Rileva la lingua dell'input per assicurarsi che sia tra quelle supportate e applica policy specifiche per ogni lingua.
-   **PII Filter**: Identifica e anonimizza informazioni personali sensibili (es. email, numeri di telefono, codici fiscali) per proteggere la privacy dell'utente e garantire la conformità al GDPR.
-   **Prompt Injection**: Rileva e blocca i tentativi di "prompt injection", in cui un utente malintenzionato cerca di manipolare il comportamento del modello AI con istruzioni nascoste.

### Guardrails di Output

Questi guardrail analizzano la risposta generata dal modello AI prima che venga mostrata all'utente.

-   **Harmful Content Filter**: Filtra contenuti dannosi, offensivi o inappropriati (es. violenza, incitamento all'odio, disinformazione). Se rileva un problema, sostituisce la risposta con un messaggio sicuro.
-   **Bias & Fairness Mitigation**: Analizza l'output per identificare e correggere bias legati a genere, etnia o età, promuovendo un linguaggio equo e inclusivo.

## Modifiche al Prompt del Chatbot

### Obiettivo
Modificare il prompt del chatbot per evitare frasi che rivelano la sua natura AI, come "in base ai documenti in mio possesso", "in base al contesto", ecc. Il chatbot deve comportarsi come un assistente tecnico qualificato e professionale.

### Modifiche Apportate

#### 1. Modifica del Prompt Principale
**File:** `pdf_chatbot_prodotti.py` (righe 364-400)

**Prima:**
```
Sei un assistente virtuale specializzato nell'analisi di manuali tecnici...
```

**Dopo:**
```
Sei un esperto tecnico specializzato in questo prodotto. Hai una conoscenza approfondita di tutte le specifiche tecniche, procedure e caratteristiche del prodotto...
```

##### Cambiamenti chiave:
- Cambiato da "assistente virtuale" a "esperto tecnico"
- Aggiunto che "conosce direttamente" il prodotto
- Inclusi esempi specifici di frasi da evitare
- Aggiunti esempi di risposte corrette

#### 2. Istruzioni Specifiche Aggiunte
- **NON usare mai:** "Gentile utente", "in base alla documentazione", "secondo i manuali", ecc.
- **Usare invece:** "Per questo problema...", "La procedura corretta è...", "Ti consiglio di..."
- **Esempi concreti** di cosa fare e cosa evitare

#### 3. Funzione di Post-Processing
**Aggiunta:** `_clean_ai_references()` (righe 414-455)

Questa funzione rimuove automaticamente:
- "Gentile utente"
- "in base alla documentazione"
- "in base ai documenti"
- "secondo i manuali"
- "dalle informazioni disponibili"
- "in base al contesto"
- E molte altre varianti

##### Caratteristiche:
- Usa regex per identificare pattern problematici
- Pulisce spazi multipli e virgole orfane
- Capitalizza automaticamente la prima lettera
- Gestisce casi edge come virgole iniziali

#### 4. Modifica del Messaggio di Errore
**Prima:**
```
❌ Non ho trovato informazioni rilevanti nei documenti per questo prodotto.
```

**Dopo:**
```
❌ Mi dispiace, non riesco a trovare informazioni specifiche per rispondere alla tua domanda su questo prodotto.
```

### File di Test Creati

#### 1. `test_prompt_changes.py`
- Testa la funzione `_clean_ai_references()`
- Verifica che le frasi problematiche vengano rimosse correttamente
- Include 5 test cases diversi

#### 2. `test_chatbot_responses.py`
- Testa il chatbot completo (se le API key sono configurate)
- Verifica che le risposte non contengano frasi problematiche
- Fornisce istruzioni per test manuali

### Come Testare le Modifiche

#### Test Automatici
```bash
# Test della funzione di pulizia
python test_prompt_changes.py

# Test del chatbot completo (richiede API keys)
python test_chatbot_responses.py
```

#### Test Manuali
1. Configura le API keys nel file `.env`
2. Aggiungi documenti PDF in `pdf/CODICE_PRODOTTO/`
3. Avvia l'applicazione: `python app.py`
4. Testa varie domande nell'interfaccia web
5. Verifica che le risposte non contengano frasi come:
   - "Gentile utente"
   - "in base alla documentazione"
   - "secondo i manuali"
   - "dalle informazioni disponibili"

### Risultato Atteso

#### Prima delle Modifiche
```
Gentile utente, in base alla documentazione disponibile, 
il prodotto funziona nel seguente modo...
```

#### Dopo le Modifiche
```
Per questo prodotto, la procedura di funzionamento prevede...
```

### Note Tecniche

- Le modifiche sono retrocompatibili
- Non influenzano altre funzionalità del chatbot
- Il post-processing è efficiente e non rallenta le risposte
- I link ai documenti vengono mantenuti correttamente
- La funzione di pulizia è case-insensitive

### Monitoraggio

Per verificare l'efficacia delle modifiche:
1. Monitora le risposte del chatbot in produzione
2. Raccogli feedback dagli utenti
3. Aggiungi nuovi pattern alla funzione di pulizia se necessario
4. Considera di aggiungere logging per tracciare le sostituzioni effettuate

### Possibili Miglioramenti Futuri

1. **Machine Learning**: Usare un modello per identificare automaticamente frasi "robotiche"
2. **A/B Testing**: Confrontare le risposte prima e dopo le modifiche
3. **Feedback Loop**: Permettere agli utenti di segnalare risposte che sembrano "artificiali"
4. **Personalizzazione**: Adattare il tono in base al tipo di utente o prodotto

## Configurazione Avanzata

### Livelli di Verbosità

Puoi modificare il comportamento del chatbot cambiando il valore di `VERBOSITY_LEVEL` nel file `.env`:

-   **1 (Conciso)**: Risposte brevi e dirette.
-   **2 (Poco Dettagliato)**: Risposte con brevi spiegazioni.
-   **3 (Bilanciato)**: Il giusto equilibrio tra dettaglio e concisione (default).
-   **4 (Articolato)**: Risposte complete che approfondiscono l'argomento.
-   **5 (Esaustivo)**: Spiegazioni approfondite con esempi e contesto aggiuntivo.

## Endpoint API

L'applicazione espone alcuni endpoint API per la gestione della chat:

-   `POST /prepare`
    -   Prepara i documenti per un dato codice prodotto.
    -   **Payload**: `{"product_code": "CODICE_X"}`
    -   **Risposta**: `{"success": true/false, "message": "..."}`

-   `POST /chat`
    -   Invia un messaggio al chatbot e riceve una risposta.
    -   **Payload**: `{"message": "...", "product_code": "CODICE_X", "history": [...]}`
    -   **Risposta**: `{"answer": "...", "history": [...]}`

# Correzione Bug: Link ai Documenti Rimossi dalle Risposte

## Problema Identificato

I riferimenti ai documenti nelle risposte dell'assistente virtuale non avevano più i link cliccabili. I documenti venivano citati ma senza i link diretti al documento e alla pagina specifica.

## Causa del Problema

Il bug era causato da un errore nella gestione dei risultati dei guardrails di output nel file `pdf_chatbot_prodotti.py`, riga 509:

```python
# CODICE ERRATO
answer = output_result.get("modified_text", answer)
```

Il problema era che:
1. Il `GuardrailManager` restituisce sempre un campo `text` che contiene il testo processato (modificato o originale)
2. Il campo `modified_text` esiste solo quando i guardrails modificano effettivamente il testo
3. Quando i guardrails non modificano il testo, `modified_text` è `None` o non esiste
4. Il codice cercava `modified_text` invece di `text`, causando la perdita del contenuto processato

## Soluzione Implementata

Modificata la riga 509 in `pdf_chatbot_prodotti.py`:

```python
# CODICE CORRETTO
answer = output_result.get("text", answer)
```

Questa modifica garantisce che:
- Venga sempre utilizzato il campo `text` che contiene il testo processato dai guardrails
- I link ai documenti vengano preservati correttamente
- Il sistema funzioni sia quando i guardrails modificano il testo sia quando non lo modificano

## Struttura del GuardrailManager

Il `GuardrailManager.process_output()` restituisce sempre un oggetto con questa struttura:

```python
{
    "text": "testo processato (modificato o originale)",
    "original_text": "testo originale",
    "blocked": False,
    "modified": False,
    "warnings": [],
    "metadata": {...}
}
```

## Test di Verifica

### Test Manuale
Creato e eseguito `test_links_preservation.py` che:
- Inizializza il chatbot
- Esegue una query che genera riferimenti ai documenti
- Verifica che i link siano presenti nella risposta
- **Risultato**: ✅ 13 link ai documenti trovati e preservati

### Test di Regressione
Creato `tests/test_document_links_regression.py` con 4 test:
1. `test_links_preserved_in_response` - Verifica preservazione link in risposte reali
2. `test_guardrails_dont_remove_links` - Verifica che i guardrails non rimuovano i link
3. `test_output_result_structure` - Verifica struttura corretta del risultato guardrails
4. `test_multiple_links_preservation` - Verifica preservazione di più link nella stessa risposta

**Risultato**: ✅ Tutti i test passati

## Formato dei Link ai Documenti

I link ai documenti hanno il formato:
```markdown
[📄 Nome_File.pdf - Pag. X](/pdf/CodiceProducto/Nome_File.pdf#page=X)
```

Esempio:
```markdown
[📄 Symphony ST125 200 E5 Manuale uso e manutenzione.pdf - Pag. 17](/pdf/ProdottoA/Symphony%20ST125%20200%20E5%20Manuale%20uso%20e%20manutenzione.pdf#page=17)
```

## Funzioni Coinvolte

1. **`_create_file_link()`** - Genera il link al documento
2. **`_generate_answer()`** - Crea i riferimenti con link nei context_parts
3. **`_clean_ai_references()`** - Protegge i link durante la pulizia del testo
4. **`search_and_answer()`** - Gestisce il flusso completo e applica i guardrails

## Prevenzione Futura

- Aggiunto test di regressione automatico
- Documentato il comportamento corretto del GuardrailManager
- Chiarito l'uso dei campi `text` vs `modified_text`

## Verifica della Correzione

La correzione è stata verificata sia:
- **Programmaticamente**: tramite test automatici
- **Nell'interfaccia web**: testato manualmente nel browser
- **Nei log**: confermato che i link vengono preservati durante tutto il flusso

## File Modificati

- `pdf_chatbot_prodotti.py` (riga 509)
- `test_links_preservation.py` (nuovo)
- `tests/test_document_links_regression.py` (nuovo)
- `tests/__init__.py` (nuovo)

## Stato

✅ **RISOLTO** - I link ai documenti vengono ora preservati correttamente in tutte le risposte dell'assistente virtuale.

---

# Sistema di Modale Fullscreen per Link

## Panoramica

Il sistema di modale fullscreen sostituisce l'apertura dei link in nuove schede (`target="_blank"`) con una modale elegante e funzionale che si apre a schermo intero. Questo migliora l'esperienza utente mantenendo il contesto della conversazione del chatbot.

## Caratteristiche Implementate

### ✅ Funzionalità Core
- **Modale fullscreen**: Apertura dei link in una modale che occupa tutto lo schermo
- **Header con controlli**: Pulsanti per navigazione (indietro, avanti, ricarica) e chiusura
- **Iframe integrato**: Caricamento del contenuto del link all'interno della modale
- **Loader animato**: Indicatore di caricamento durante l'apertura dei link
- **Gestione errori**: Messaggi informativi per errori di caricamento con suggerimenti
- **Cronologia di navigazione**: Supporto per navigazione avanti/indietro all'interno della modale

### ✅ Controlli di Navigazione
- **Pulsante Indietro**: Torna alla pagina precedente nella cronologia della modale
- **Pulsante Avanti**: Vai alla pagina successiva (se disponibile)
- **Pulsante Ricarica**: Ricarica la pagina corrente
- **Pulsante Chiudi**: Chiude la modale e torna al chatbot

### ✅ Gestione Errori e Loader
- **Loader con animazione**: Spinner rotante con testo informativo
- **Timeout di caricamento**: 30 secondi di timeout per evitare attese infinite
- **Messaggi di errore specifici**: Diversi messaggi per diversi tipi di errore
- **Pulsante Riprova**: Possibilità di ritentare il caricamento
- **Validazione URL**: Controllo della validità degli URL prima del caricamento

### ✅ Accessibilità
- **Attributi ARIA**: Supporto completo per screen reader
- **Focus trap**: Il focus rimane all'interno della modale quando aperta
- **Gestione focus**: Ripristino del focus precedente alla chiusura
- **Tasti di scelta rapida**: 
  - `Esc`: Chiude la modale
  - `F5`: Ricarica la pagina
  - `Alt + ←`: Vai indietro
  - `Alt + →`: Vai avanti

### ✅ Design Responsive
- **Desktop**: Modale con dimensioni ottimali e controlli completi
- **Tablet**: Adattamento delle dimensioni e spaziature
- **Mobile**: Layout ottimizzato per schermi piccoli
- **Media queries**: Supporto per schermi da 480px a desktop

### ✅ Integrazione
- **Compatibilità**: Funziona con entrambe le versioni del chatbot (principale e embedded)
- **File condiviso**: Un unico file JavaScript (`link-modal.js`) per entrambe le versioni
- **Stili separati**: CSS specifici per ogni versione mantenendo coerenza

## Struttura dei File

```
static/
├── js/
│   ├── link-modal.js      # Logica principale della modale (condivisa)
│   ├── script.js          # Script principale del chatbot
│   └── embedded.js        # Script per versione embedded
├── css/
│   ├── style.css          # Stili per versione principale (con stili modale)
│   └── embedded.css       # Stili per versione embedded (con stili modale)
templates/
├── index.html             # Template principale (con HTML modale)
└── embedded.html          # Template embedded (con HTML modale)
```

## Come Funziona

### 1. Intercettazione dei Link
Il sistema intercetta automaticamente tutti i click sui link nei messaggi del chatbot:

```javascript
document.addEventListener('click', (e) => {
    if (e.target.tagName === 'A' && e.target.href) {
        if (this.shouldOpenInModal(url)) {
            e.preventDefault();
            this.openLink(e.target.href, e.target.textContent);
        }
    }
});
```

### 2. Logica di Apertura
La modale si apre per:
- File PDF
- Link dello stesso dominio
- Domini di documentazione comuni (GitHub, Stack Overflow, MDN, etc.)

### 3. Gestione della Cronologia
La modale mantiene una cronologia interna separata dalla cronologia del browser:

```javascript
// Aggiungi alla cronologia
this.history.push(url);
this.currentIndex = this.history.length - 1;
```

### 4. Gestione degli Errori
Sistema robusto di gestione errori con messaggi specifici:

```javascript
if (message.includes('Timeout')) {
    suggestions = 'La pagina potrebbe essere lenta o non disponibile.';
} else if (message.includes('404')) {
    suggestions = 'La pagina richiesta non esiste o è stata spostata.';
}
```

## Tasti di Scelta Rapida

| Tasto | Azione |
|-------|--------|
| `Esc` | Chiude la modale |
| `F5` | Ricarica la pagina corrente |
| `Alt + ←` | Vai alla pagina precedente |
| `Alt + →` | Vai alla pagina successiva |
| `Tab` | Naviga tra i controlli (con focus trap) |

## Personalizzazione

### Timeout di Caricamento
```javascript
this.maxLoadTime = 30000; // 30 secondi (modificabile)
```

### Domini per Apertura in Modale
```javascript
const docDomains = [
    'docs.google.com',
    'github.com',
    'gitlab.com',
    // Aggiungi altri domini qui
];
```

### Stili CSS
Gli stili sono completamente personalizzabili modificando le variabili CSS:

```css
.modal-container {
    width: 95%;
    height: 95%;
    max-width: 1400px;
    max-height: 900px;
}
```

## Compatibilità Browser

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Note Tecniche

### CORS e Sicurezza
- La modale rispetta le politiche CORS
- Non può accedere al contenuto di domini esterni per motivi di sicurezza
- I titoli delle pagine esterne potrebbero non essere disponibili

### Performance
- Caricamento lazy degli iframe
- Cleanup automatico delle risorse alla chiusura
- Gestione memoria ottimizzata

### Accessibilità
- Conforme alle linee guida WCAG 2.1
- Supporto completo per screen reader
- Navigazione da tastiera ottimizzata
- Focus management robusto

## Risoluzione Problemi

### La modale non si apre
1. Verifica che il JavaScript sia caricato correttamente
2. Controlla la console per errori
3. Assicurati che l'URL sia valido

### Errori di caricamento
1. Verifica la connessione internet
2. Controlla se l'URL è accessibile
3. Alcuni siti potrebbero bloccare l'embedding in iframe

### Problemi di responsive
1. Verifica le media queries CSS
2. Testa su diversi dispositivi
3. Controlla il viewport meta tag

## Manutenzione

### Aggiornamenti Futuri
- Monitorare le performance di caricamento
- Aggiornare la lista dei domini supportati
- Migliorare la gestione errori basata sui feedback utenti

### Log e Debug
Il sistema include logging dettagliato per il debug:
```javascript
console.log('CORS restriction - cannot access iframe content');
```

---

# Correzioni Finali Applicate al Sistema di Modale Fullscreen

## Problemi Risolti (Aggiornamento Finale)

### 1. ✅ Modale a Tutta Pagina
**Problema**: La modale aveva margini e non occupava tutto lo schermo
**Soluzione**: 
- Rimosso `width: 95%` e `height: 95%` 
- Impostato `width: 100%` e `height: 100%`
- Rimosso `max-width`, `max-height`, `border-radius` e `box-shadow`
- Rimosso le media queries che riducevano ulteriormente la modale su mobile

**File modificati**:
- `static/css/style.css` - `.modal-container`
- `static/css/embedded.css` - `.modal-container`

### 2. ✅ Intestazioni Non Leggibili
**Problema**: Testo bianco su sfondo bianco nell'header della modale
**Soluzione**: 
- Aggiunto `color: white` esplicito a `.modal-title`
- Aggiunto `color: white` a `.modal-title h1`
- Aggiunto `color: white` nelle media queries per mobile

**File modificati**:
- `static/css/style.css` - `.modal-title` e media queries
- `static/css/embedded.css` - `.modal-title` e media queries

### 3. ✅ Controlli PDF Viewer Nascosti
**Problema**: Interfaccia del PDF viewer del browser visibile sopra il documento
**Soluzioni Multiple**:

#### A. Parametri URL per PDF
- Aggiunto parametri `toolbar=0&navpanes=0&scrollbar=0` agli URL dei PDF
- Implementato in `static/js/link-modal.js` nella funzione `loadUrl()`

#### B. CSS per Nascondere Controlli
- Creato wrapper `.pdf-wrapper` che nasconde i controlli
- Iframe spostato verso l'alto di 40px con `top: -40px`
- Altezza aumentata con `height: calc(100% + 40px)`
- Overlay bianco sopra per coprire i controlli residui

#### C. Applicazione Automatica
- JavaScript rileva automaticamente i PDF e applica il wrapper
- Rimozione automatica del wrapper quando si chiude o cambia contenuto

**File modificati**:
- `static/js/link-modal.js` - Funzioni `loadUrl()`, `showIframe()`, `hideIframe()`
- `static/css/style.css` - Stili `.pdf-wrapper` (RIMOSSI)
- `static/css/embedded.css` - Stili `.pdf-wrapper` (RIMOSSI)

### 4. **Intestazioni e Controlli Visibili** ✅
- **Problema**: Intestazioni non leggibili (testo bianco su sfondo bianco) e X di chiusura invisibile
- **Soluzione**:
  - Cambiato colore di sfondo header da blu a grigio chiaro (`#f8f9fa`)
  - Cambiato colore testo da bianco a nero (`#333`)
  - Aggiornato stile pulsanti per essere visibili con sfondo chiaro
  - Applicato a entrambi i file CSS (style.css e embedded.css)

### 5. **Controlli PDF Ripristinati** ✅
- **Problema**: Controlli PDF nativi del browser spariti
- **Soluzione**:
  - Rimossi parametri URL che nascondevano i controlli (`toolbar=0&navpanes=0&scrollbar=0`)
  - Rimosso wrapper CSS che nascondeva la toolbar del PDF
  - Semplificato caricamento iframe per compatibilità massima

### 6. **Gestione Errori Migliorata** ✅
- **Problema**: Errori di caricamento generici
- **Soluzione**:
  - Ridotto timeout a 15 secondi per evitare attese eccessive
  - Aggiunto pulsante "Apri in nuova scheda" quando iframe fallisce
  - Migliorati messaggi di errore con suggerimenti specifici
  - Rimossa logica di filtro URL che causava problemi

## Stato Finale

✅ **Modale fullscreen funzionante**
✅ **Intestazioni e controlli visibili**
✅ **Controlli PDF nativi disponibili**
✅ **Gestione errori robusta con fallback**
✅ **Compatibilità cross-browser migliorata**

## Test Effettuati

- ✅ Caricamento PDF con controlli nativi visibili
- ✅ Intestazioni leggibili su sfondo chiaro
- ✅ Pulsante chiusura (X) visibile e funzionante
- ✅ Fallback "Apri in nuova scheda" per link problematici
- ✅ Responsive design mantenuto

## Prossimi Passi

1. **Test esteso**: Verificare su più browser e dispositivi
2. **Monitoraggio**: Osservare eventuali nuovi errori in produzione
3. **Ottimizzazione**: Migliorare performance se necessario

## Dettagli Tecnici delle Correzioni

### Modale Fullscreen
```css
.modal-container {
    position: relative;
    width: 100%;           /* Era 95% */
    height: 100%;          /* Era 95% */
    background-color: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* Rimossi: max-width, max-height, border-radius, box-shadow */
}
```

### Correzione Colore Testo
```css
.modal-title {
    /* ... altre proprietà ... */
    color: white;          /* Aggiunto */
}

.modal-title h1 {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
    color: white;          /* Aggiunto */
}
```

### Nascondere Controlli PDF
```css
.pdf-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.pdf-wrapper iframe {
    position: relative;
    top: -40px;            /* Sposta iframe verso l'alto */
    height: calc(100% + 40px); /* Compensa l'altezza */
    width: 100%;
}

.pdf-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;          /* Copre i controlli */
    background: #f8f9fa;
    z-index: 10;
    pointer-events: none;
    border-bottom: 1px solid #dee2e6;
}
```

### JavaScript per PDF
```javascript
// In loadUrl()
if (url.toLowerCase().includes('.pdf')) {
    const separator = url.includes('?') ? '&' : '?';
    finalUrl = url + separator + 'toolbar=0&navpanes=0&scrollbar=0';
}

// In showIframe()
if (this.currentUrl && this.currentUrl.toLowerCase().includes('.pdf')) {
    const contentArea = document.querySelector('.modal-content-area');
    if (contentArea && !contentArea.classList.contains('pdf-wrapper')) {
        contentArea.classList.add('pdf-wrapper');
    }
}
```

## Risultati Ottenuti

1. **Modale Fullscreen**: Ora occupa completamente lo schermo su tutti i dispositivi
2. **Testo Leggibile**: Le intestazioni sono ora chiaramente visibili in bianco
3. **PDF Puliti**: I controlli del browser sono nascosti o minimizzati significativamente

## Compatibilità Browser

- ✅ **Chrome**: Parametri URL funzionano, wrapper CSS come backup
- ✅ **Firefox**: Wrapper CSS nasconde efficacemente i controlli
- ✅ **Safari**: Combinazione di entrambi gli approcci
- ✅ **Edge**: Parametri URL supportati
- ✅ **Mobile**: Wrapper CSS funziona su tutti i browser mobile

## Note Tecniche

### Limitazioni
- Alcuni browser potrebbero ancora mostrare controlli minimi
- I parametri URL per PDF non sono supportati da tutti i server
- Il wrapper CSS è un workaround visivo, non rimuove funzionalmente i controlli

### Fallback
- Se i parametri URL non funzionano, il wrapper CSS fornisce una soluzione visiva
- Se il wrapper CSS non viene applicato, i controlli rimangono visibili ma funzionali
- La modale rimane completamente utilizzabile in tutti i casi

## Test Consigliati

1. **Test su diversi browser**: Chrome, Firefox, Safari, Edge
2. **Test su mobile**: iOS Safari, Chrome Mobile, Samsung Internet
3. **Test con diversi PDF**: Locali e remoti
4. **Test di navigazione**: Indietro/avanti nella modale
5. **Test di accessibilità**: Navigazione da tastiera e screen reader

## Manutenzione Futura

- Monitorare aggiornamenti dei browser che potrebbero cambiare il comportamento dei PDF
- Considerare soluzioni alternative come PDF.js per controllo completo
- Aggiornare i parametri URL se nuove opzioni diventano disponibili
- Testare periodicamente su nuove versioni dei browser

---

# Sistema di Logging delle Conversazioni

Questo documento descrive il sistema di logging delle conversazioni implementato per l'applicazione chat-jina.

## Panoramica

Il sistema di logging delle conversazioni è progettato per registrare automaticamente tutte le interazioni tra gli utenti e il chatbot in un database MySQL. Il sistema include:

- Middleware per catturare automaticamente le interazioni
- Gestione asincrona del salvataggio per non rallentare le risposte
- Meccanismo di retry per le scritture fallite
- API per query e statistiche sulle conversazioni

## Schema del Database

```sql
CREATE TABLE conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    product VARCHAR(255),
    question TEXT NOT NULL,
    response TEXT NOT NULL,
    guardrail_log JSON,
    session_id VARCHAR(255),
    user_agent TEXT,
    response_time_ms INT,
    confidence_score FLOAT,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip (ip_address),
    INDEX idx_product (product),
    INDEX idx_session (session_id)
);
```

## Configurazione

La configurazione del database viene gestita tramite variabili d'ambiente nel file `.env`:

```
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=chat_jina
DB_USER=prova
DB_PASSWORD=prova
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
```

## Installazione

1. Assicurati che MySQL sia installato e in esecuzione sul tuo sistema
2. Aggiungi le variabili di configurazione del database al file `.env`
3. Installa le dipendenze richieste:
   ```
   pip install mysql-connector-python asyncio aiofiles
   ```
4. Esegui lo script di setup per creare il database e testare il sistema:
   ```
   python setup_logging.py
   ```

## Architettura

Il sistema di logging è composto da diversi moduli:

### 1. Database Manager (`database/db_manager.py`)

Gestisce le connessioni al database e le operazioni CRUD:
- Connection pooling per ottimizzare le prestazioni
- Gestione degli errori e delle eccezioni
- Metodi per inserire, recuperare e analizzare i dati delle conversazioni

### 2. Conversation Logger (`logging_service/conversation_logger.py`)

Gestisce il logging asincrono delle conversazioni:
- Code per il buffering delle richieste di logging
- Worker thread per l'elaborazione asincrona
- Meccanismo di retry per gestire i fallimenti temporanei
- Shutdown graceful per evitare la perdita di dati

### 3. Middleware Flask (`middleware/conversation_middleware.py`)

Intercetta automaticamente le richieste HTTP:
- Cattura i dettagli delle richieste e delle risposte
- Estrae informazioni rilevanti dalle conversazioni
- Invia i dati al Conversation Logger in modo asincrono

### 4. API per Statistiche (`api/conversation_api.py`)

Fornisce endpoint per interrogare e analizzare i dati delle conversazioni:
- `/api/conversations/stats` - Statistiche generali
- `/api/conversations/search` - Ricerca conversazioni
- `/api/conversations/daily-stats` - Statistiche giornaliere
- `/api/conversations/products` - Statistiche per prodotto
- `/api/conversations/health` - Health check del sistema

## Utilizzo

### Logging Automatico

Il sistema registra automaticamente tutte le interazioni con l'endpoint `/chat`. Non è necessaria alcuna modifica al codice esistente per abilitare questa funzionalità.

### Query e Statistiche

Puoi utilizzare gli endpoint API per ottenere statistiche e cercare conversazioni:

```python
import requests

# Ottieni statistiche generali
response = requests.get('http://localhost:5001/api/conversations/stats')
stats = response.json()

# Cerca conversazioni per un prodotto specifico
response = requests.get('http://localhost:5001/api/conversations/search?product=CODICE_A&limit=10')
conversations = response.json()

# Ottieni statistiche giornaliere degli ultimi 7 giorni
response = requests.get('http://localhost:5001/api/conversations/daily-stats?days=7')
daily_stats = response.json()
```

## Monitoraggio e Manutenzione

### Health Check

Puoi verificare lo stato del sistema di logging utilizzando l'endpoint di health check:

```
GET /api/conversations/health
```

Questo endpoint restituisce informazioni sullo stato del database e delle code di logging.

### Gestione delle Code

Il sistema utilizza code in memoria per il buffering delle richieste di logging. In caso di problemi, puoi verificare lo stato delle code tramite l'endpoint di health check.

### Backup del Database

Si consiglia di configurare backup regolari del database MySQL per evitare la perdita di dati. Esempio di comando per il backup:

```bash
mysqldump -u prova -p chat_jina > chat_jina_backup_$(date +%Y%m%d).sql
```

## Estensioni Future

Il sistema di logging può essere esteso in vari modi:

1. **Dashboard di Analisi**: Creare un'interfaccia web per visualizzare le statistiche
2. **Esportazione Dati**: Aggiungere funzionalità per esportare i dati in CSV o JSON
3. **Alerting**: Implementare notifiche per pattern anomali nelle conversazioni
4. **Integrazione con Sistemi di Monitoraggio**: Esporre metriche per Prometheus o altri sistemi

## Risoluzione dei Problemi

### Problemi di Connessione al Database

Se l'applicazione non riesce a connettersi al database:

1. Verifica che MySQL sia in esecuzione
2. Controlla le credenziali nel file `.env`
3. Assicurati che l'utente abbia i permessi necessari

### Problemi di Performance

Se noti rallentamenti:

1. Aumenta `DB_POOL_SIZE` nel file `.env`
2. Verifica che gli indici del database siano utilizzati correttamente
3. Considera l'aggiunta di indici aggiuntivi in base ai pattern di query

### Errori di Logging

Se le conversazioni non vengono registrate:

1. Controlla i log dell'applicazione per errori
2. Verifica lo stato del sistema con l'endpoint di health check
3. Controlla lo spazio disponibile sul disco per il database
