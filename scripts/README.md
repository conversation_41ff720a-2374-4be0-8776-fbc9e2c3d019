# Sistema di Embedding Automatizzato

Questo sistema fornisce un'infrastruttura completa per l'embedding automatizzato di documenti nella knowledge base del chatbot.

## 🚀 Caratteristiche Principali

- **Scansione ricorsiva**: Esplora automaticamente tutte le directory in `pdf/` per trovare documenti supportati
- **Modalità incrementale**: Processa solo documenti nuovi o modificati usando hash MD5
- **Progress bar e logging**: Monitoraggio dettagliato delle operazioni con progress bar visiva
- **Report dettagliati**: Statistiche complete con tempi, errori e performance
- **Scheduling automatico**: Esecuzione programmata con configurazione flessibile
- **Esecuzione in background**: Supporto per daemon e scheduling
- **Gestione errori robusta**: Retry automatico e continuazione in caso di errori

## 📁 Struttura File

```
scripts/
├── embedding_config.py      # Configurazioni centrali
├── embedding_utils.py       # Utilità condivise
├── automated_embedding.py   # Script principale per embedding
├── embedding_scheduler.py   # Sistema di scheduling
├── embedding_manager.py     # Manager unificato
└── README.md                # Questa documentazione
```

## 🔧 Installazione

### Prerequisiti

Assicurati di avere installato tutte le dipendenze:

```bash
pip install tqdm psutil
```

Le altre dipendenze dovrebbero già essere presenti dal sistema principale.

### Configurazione

1. **Variabili d'ambiente**: Assicurati che siano configurate:
   ```bash
   export JINA_API_KEY="your_jina_api_key"
   export GEMINI_API_KEY="your_gemini_api_key"
   ```

2. **Directory**: Il sistema usa la struttura esistente:
   ```
   pdf/
   ├── CODICE_PRODOTTO_A/
   ├── CODICE_PRODOTTO_B/
   └── ...
   ```

## 📖 Utilizzo

### Manager Unificato (Raccomandato)

Il modo più semplice per usare il sistema è attraverso il manager unificato:

```bash
# Mostra lo stato del sistema
python scripts/embedding_manager.py status

# Esegue embedding una volta
python scripts/embedding_manager.py run

# Esegue embedding forzando il riprocessamento
python scripts/embedding_manager.py run --force-reprocess

# Avvia scheduling automatico ogni ora
python scripts/embedding_manager.py schedule start --interval 3600

# Avvia scheduling con orario specifico
python scripts/embedding_manager.py schedule start --schedule-time 02:00 --daemon

# Ferma lo scheduler
python scripts/embedding_manager.py schedule stop

# Mostra stato dello scheduler
python scripts/embedding_manager.py schedule status

# Lista report recenti
python scripts/embedding_manager.py reports list --last 5

# Mostra report specifico
python scripts/embedding_manager.py reports show embedding_report_20241221_143022.json

# Pulisce file vecchi
python scripts/embedding_manager.py cleanup --older-than 30
```

### Script Individuali

#### Embedding Manuale

```bash
# Embedding base
python scripts/automated_embedding.py

# Con opzioni avanzate
python scripts/automated_embedding.py \
    --force-reprocess \
    --verbose \
    --product-code CODICE_PRODOTTO_A

# Simulazione (dry run)
python scripts/automated_embedding.py --dry-run

# Esecuzione in background
python scripts/automated_embedding.py --background
```

#### Scheduling Automatico

```bash
# Avvia scheduler ogni ora
python scripts/embedding_scheduler.py --interval 3600

# Avvia scheduler con orario specifico
python scripts/embedding_scheduler.py --schedule 02:00 --daemon

# Mostra stato
python scripts/embedding_scheduler.py --status

# Ferma scheduler
python scripts/embedding_scheduler.py --stop
```

## ⚙️ Configurazione

### File di Configurazione

Il sistema usa diversi file di configurazione:

- `scripts/embedding_config.py`: Configurazioni principali
- `schedule_config.json`: Configurazione scheduling (creato automaticamente)
- `pdf_metadata.json`: Metadati dei file (gestito automaticamente)

### Parametri Principali

```python
# In embedding_config.py
CHUNK_SIZE = 1000           # Dimensione chunk di testo
CHUNK_OVERLAP = 200         # Sovrapposizione tra chunk
BATCH_SIZE = 50             # Dimensione batch per ChromaDB
MAX_RETRIES = 3             # Tentativi massimi per API
```

### Estensioni Supportate

Il sistema supporta i seguenti formati:
- PDF (`.pdf`)
- Testo (`.txt`)
- Markdown (`.md`)
- CSV (`.csv`)
- Word (`.doc`, `.docx`) - *in sviluppo*

## 📊 Monitoraggio e Report

### Log

I log sono salvati in `logs/` con rotazione automatica:
- `automated_embedding.log`: Log dell'embedding
- `embedding_scheduler.log`: Log dello scheduler
- `embedding_manager.log`: Log del manager

### Report

I report sono salvati in `reports/` in formato JSON:
- Statistiche di elaborazione
- Performance del sistema
- Errori e problemi
- Configurazione usata

Esempio di report:
```json
{
  "timestamp": "2024-12-21T14:30:22",
  "processing_stats": {
    "total_files": 150,
    "processed_files": 148,
    "failed_files": 2,
    "total_chunks": 5420,
    "processing_time_seconds": 1847.5,
    "success_rate": 98.7
  },
  "performance_stats": {
    "memory_usage_mb": 245.8,
    "api_calls": 108,
    "elapsed_time": 1847.5
  }
}
```

## 🔄 Modalità Incrementale

Il sistema traccia automaticamente i file usando hash MD5:

1. **Prima esecuzione**: Tutti i file vengono processati
2. **Esecuzioni successive**: Solo file nuovi o modificati
3. **Force reprocess**: Forza il riprocessamento di tutti i file

Gli hash sono salvati in `pdf_metadata.json` e aggiornati automaticamente.

## 🕐 Scheduling

### Configurazione Scheduling

```json
{
  "enabled": true,
  "interval_seconds": 3600,
  "scheduled_times": ["02:00", "14:00"],
  "max_concurrent_jobs": 1,
  "retry_failed_jobs": true,
  "max_retries": 3,
  "embedding_options": {
    "background": true,
    "force_reprocess": false
  }
}
```

### Gestione come Servizio

Per eseguire come servizio di sistema (Linux):

1. Crea file di servizio `/etc/systemd/system/embedding-scheduler.service`:
```ini
[Unit]
Description=Embedding Scheduler
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/python3 scripts/embedding_scheduler.py --daemon
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

2. Abilita e avvia:
```bash
sudo systemctl enable embedding-scheduler
sudo systemctl start embedding-scheduler
```

## 🛠️ Troubleshooting

### Problemi Comuni

1. **API Key non configurate**:
   ```
   ❌ JINA_API_KEY non trovata nelle variabili d'ambiente
   ```
   Soluzione: Configura le variabili d'ambiente

2. **Directory PDF non trovata**:
   ```
   ❌ Directory PDF non trovata: pdf
   ```
   Soluzione: Crea la directory `pdf/` nella root del progetto

3. **Errori di memoria**:
   - Riduci `BATCH_SIZE` in `embedding_config.py`
   - Aumenta `SLEEP_BETWEEN_BATCHES`

4. **Errori di rete**:
   - Aumenta `MAX_RETRIES`
   - Verifica connessione internet

### Debug

Per debug dettagliato:
```bash
python scripts/automated_embedding.py --verbose
```

### Pulizia

Per pulire file temporanei e vecchi:
```bash
python scripts/embedding_manager.py cleanup --older-than 30
```

## 🔮 Sviluppi Futuri

- [ ] Supporto per documenti Word (.doc, .docx)
- [ ] Elaborazione immagini con OCR
- [ ] Interfaccia web per monitoraggio
- [ ] Notifiche email per errori
- [ ] Backup automatico del database
- [ ] Clustering per elaborazione distribuita

## 📞 Supporto

Per problemi o domande:
1. Controlla i log in `logs/`
2. Verifica la configurazione con `python scripts/embedding_manager.py status`
3. Consulta questa documentazione
4. Contatta il team di sviluppo
